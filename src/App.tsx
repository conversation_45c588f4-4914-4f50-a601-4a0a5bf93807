import React, { useState, useEffect } from 'react';
import {
  Config<PERSON>rovider,
  Layout,
  Menu,
  Typo<PERSON>,
  Card,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  Switch,
  Steps,
  Row,
  Col,
  Divider,
  Tag,
  Progress,
  Statistic,
  Avatar,
  List,
  Alert,
  message,
  Tooltip
} from 'antd';
import {
  DashboardOutlined,
  DatabaseOutlined,
  SafetyOutlined,
  FileTextOutlined,
  PlusOutlined,
  EditOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  UserOutlined,
  DownloadOutlined,
  FilterOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { Header, Content, Sider } = Layout;
const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;
const { Step } = Steps;

// Data Models
interface Dataset {
  id: string;
  name: string;
  description: string;
  source: string;
  collectionDate: string;
  privacyClassification: 'public' | 'internal' | 'confidential' | 'restricted' | 'highly-sensitive';
  complianceStatus: 'compliant' | 'non-compliant' | 'under-review' | 'requires-attention';
  owner: string;
  tags: string[];
  consentStatus: boolean;
  retentionPeriod: number;
  createdAt: string;
  updatedAt: string;
}

interface PrivacyReview {
  id: string;
  datasetId: string;
  reviewType: 'initial' | 'periodic' | 'incident-driven' | 'regulatory';
  status: 'pending' | 'in-progress' | 'completed' | 'requires-action';
  riskLevel: 'low' | 'medium' | 'high' | 'very-high';
  reviewer: string;
  reviewDate: string;
  findings: string[];
  recommendations: string[];
  approvalStatus: 'pending' | 'approved' | 'rejected';
  createdAt: string;
}

// Utility functions
const generateId = () => Date.now().toString(36) + Math.random().toString(36).substr(2);

const formatDate = (date: string | Date) => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

// Enhanced Dashboard Component with Apple Design
const Dashboard: React.FC<{ datasets: Dataset[]; reviews: PrivacyReview[] }> = ({ datasets, reviews }) => {
  const compliantDatasets = datasets.filter(d => d.complianceStatus === 'compliant').length;
  const pendingReviews = reviews.filter(r => r.status === 'pending' || r.status === 'in-progress').length;
  const complianceScore = datasets.length > 0 ? Math.round((compliantDatasets / datasets.length) * 100) : 0;

  const recentActivities = [
    {
      icon: '✅',
      title: 'Privacy review completed for Customer Purchase History',
      description: 'Review approved with minor recommendations',
      time: '2 hours ago',
      type: 'success'
    },
    {
      icon: '📊',
      title: 'New dataset registered: User Behavior Analytics',
      description: 'Awaiting initial privacy assessment',
      time: '5 hours ago',
      type: 'info'
    },
    {
      icon: '⚠️',
      title: 'Consent withdrawal processed',
      description: '3 data subject requests processed',
      time: '1 day ago',
      type: 'warning'
    },
    {
      icon: '📋',
      title: 'Compliance report generated',
      description: 'Q2 2024 GDPR compliance report ready',
      time: '2 days ago',
      type: 'success'
    }
  ];

  return (
    <div style={{ padding: '0 4px' }}>
      <div style={{ marginBottom: '32px' }}>
        <Title level={1} style={{
          color: '#1d1d1f',
          fontSize: '34px',
          fontWeight: 700,
          margin: 0,
          marginBottom: '8px',
          letterSpacing: '-0.5px'
        }}>
          Privacy Dashboard
        </Title>
        <Text style={{
          color: '#86868b',
          fontSize: '17px',
          fontWeight: 400,
          lineHeight: '22px'
        }}>
          Overview of your data governance and privacy compliance status
        </Text>
      </div>

      {/* Key Metrics with Apple-style cards */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
        gap: '20px',
        marginBottom: '32px'
      }}>
        <Card style={{
          borderRadius: '16px',
          border: '1px solid #e5e5e7',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
          background: 'linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%)',
          color: 'white'
        }} bodyStyle={{ padding: '24px' }}>
          <Statistic
            title={<span style={{ color: 'rgba(255,255,255,0.8)', fontSize: '15px', fontWeight: 500 }}>Total Datasets</span>}
            value={datasets.length}
            valueStyle={{ color: 'white', fontSize: '36px', fontWeight: 700, lineHeight: '40px' }}
            prefix={<DatabaseOutlined style={{ color: 'rgba(255,255,255,0.9)' }} />}
          />
        </Card>

        <Card style={{
          borderRadius: '16px',
          border: '1px solid #e5e5e7',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
          background: 'linear-gradient(135deg, #34C759 0%, #30D158 100%)',
          color: 'white'
        }} bodyStyle={{ padding: '24px' }}>
          <Statistic
            title={<span style={{ color: 'rgba(255,255,255,0.8)', fontSize: '15px', fontWeight: 500 }}>Compliant Datasets</span>}
            value={compliantDatasets}
            valueStyle={{ color: 'white', fontSize: '36px', fontWeight: 700, lineHeight: '40px' }}
            prefix={<CheckCircleOutlined style={{ color: 'rgba(255,255,255,0.9)' }} />}
          />
          <Progress
            percent={complianceScore}
            showInfo={false}
            strokeColor="rgba(255,255,255,0.3)"
            trailColor="rgba(255,255,255,0.1)"
            style={{ marginTop: '12px' }}
            strokeWidth={6}
          />
        </Card>

        <Card style={{
          borderRadius: '16px',
          border: '1px solid #e5e5e7',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
          background: 'linear-gradient(135deg, #FF9500 0%, #FFCC00 100%)',
          color: 'white'
        }} bodyStyle={{ padding: '24px' }}>
          <Statistic
            title={<span style={{ color: 'rgba(255,255,255,0.8)', fontSize: '15px', fontWeight: 500 }}>Pending Reviews</span>}
            value={pendingReviews}
            valueStyle={{ color: 'white', fontSize: '36px', fontWeight: 700, lineHeight: '40px' }}
            prefix={<ClockCircleOutlined style={{ color: 'rgba(255,255,255,0.9)' }} />}
          />
        </Card>

        <Card style={{
          borderRadius: '16px',
          border: '1px solid #e5e5e7',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
          background: 'linear-gradient(135deg, #AF52DE 0%, #BF5AF2 100%)',
          color: 'white'
        }} bodyStyle={{ padding: '24px' }}>
          <Statistic
            title={<span style={{ color: 'rgba(255,255,255,0.8)', fontSize: '15px', fontWeight: 500 }}>Compliance Score</span>}
            value={complianceScore}
            suffix="%"
            valueStyle={{ color: 'white', fontSize: '36px', fontWeight: 700, lineHeight: '40px' }}
            prefix={<SafetyOutlined style={{ color: 'rgba(255,255,255,0.9)' }} />}
          />
        </Card>
      </div>

      {/* Recent Activities */}
      <Card
        title={
          <span style={{
            fontSize: '20px',
            fontWeight: 600,
            color: '#1d1d1f',
            letterSpacing: '-0.2px'
          }}>
            Recent Activities
          </span>
        }
        style={{
          borderRadius: '16px',
          border: '1px solid #e5e5e7',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)'
        }}
        bodyStyle={{ padding: '24px' }}
      >
        <List
          itemLayout="horizontal"
          dataSource={recentActivities}
          renderItem={(item) => (
            <List.Item style={{ padding: '16px 0', borderBottom: '1px solid #f5f5f7' }}>
              <List.Item.Meta
                avatar={
                  <div style={{
                    width: '44px',
                    height: '44px',
                    borderRadius: '12px',
                    background: item.type === 'success' ? '#34C759' :
                               item.type === 'warning' ? '#FF9500' : '#007AFF',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '18px'
                  }}>
                    {item.icon}
                  </div>
                }
                title={
                  <span style={{
                    fontSize: '16px',
                    fontWeight: 500,
                    color: '#1d1d1f',
                    lineHeight: '20px'
                  }}>
                    {item.title}
                  </span>
                }
                description={
                  <div>
                    <Text style={{ fontSize: '14px', color: '#86868b', lineHeight: '18px' }}>
                      {item.description}
                    </Text>
                    <br />
                    <Text style={{ fontSize: '13px', color: '#8e8e93' }}>
                      {item.time}
                    </Text>
                  </div>
                }
              />
            </List.Item>
          )}
        />
      </Card>
    </div>
  );
};

// Enhanced Dataset Management Component with Full Functionality
const DatasetManagement: React.FC<{
  datasets: Dataset[];
  onAddDataset: (dataset: Omit<Dataset, 'id' | 'createdAt' | 'updatedAt'>) => void;
  onEditDataset: (id: string, dataset: Partial<Dataset>) => void;
}> = ({ datasets, onAddDataset, onEditDataset }) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [editingDataset, setEditingDataset] = useState<Dataset | null>(null);
  const [form] = Form.useForm();

  const handleAdd = () => {
    setEditingDataset(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (dataset: Dataset) => {
    setEditingDataset(dataset);
    form.setFieldsValue({
      ...dataset,
      collectionDate: dayjs(dataset.collectionDate),
      tags: dataset.tags.join(', ')
    });
    setModalVisible(true);
  };

  const handleSubmit = async (values: any) => {
    try {
      const datasetData = {
        name: values.name,
        description: values.description,
        source: values.source,
        collectionDate: values.collectionDate.format('YYYY-MM-DD'),
        privacyClassification: values.privacyClassification,
        complianceStatus: values.complianceStatus,
        owner: values.owner,
        tags: values.tags ? values.tags.split(',').map((tag: string) => tag.trim()) : [],
        consentStatus: values.consentStatus,
        retentionPeriod: values.retentionPeriod
      };

      if (editingDataset) {
        onEditDataset(editingDataset.id, datasetData);
        message.success('Dataset updated successfully');
      } else {
        onAddDataset(datasetData);
        message.success('Dataset created successfully');
      }

      setModalVisible(false);
      form.resetFields();
    } catch (error) {
      message.error('Failed to save dataset');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'compliant': return '#34C759';
      case 'non-compliant': return '#FF3B30';
      case 'under-review': return '#FF9500';
      case 'requires-attention': return '#FFCC00';
      default: return '#8E8E93';
    }
  };

  const getClassificationColor = (classification: string) => {
    switch (classification) {
      case 'public': return '#34C759';
      case 'internal': return '#007AFF';
      case 'confidential': return '#FF9500';
      case 'restricted': return '#FF3B30';
      case 'highly-sensitive': return '#AF52DE';
      default: return '#8E8E93';
    }
  };

  return (
    <div style={{ padding: '0 4px' }}>
      <div style={{ marginBottom: '32px' }}>
        <Title level={1} style={{
          color: '#1d1d1f',
          fontSize: '34px',
          fontWeight: 700,
          margin: 0,
          marginBottom: '8px',
          letterSpacing: '-0.5px'
        }}>
          Dataset Management
        </Title>
        <Text style={{
          color: '#86868b',
          fontSize: '17px',
          fontWeight: 400,
          lineHeight: '22px'
        }}>
          Manage your customer data catalogue and metadata
        </Text>
      </div>

      <div style={{ marginBottom: '24px' }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAdd}
          size="large"
          style={{
            borderRadius: '12px',
            height: '48px',
            fontSize: '16px',
            fontWeight: 500,
            boxShadow: '0 4px 12px rgba(0, 122, 255, 0.3)'
          }}
        >
          Add New Dataset
        </Button>
      </div>

      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(400px, 1fr))',
        gap: '20px'
      }}>
        {datasets.map((dataset) => (
          <Card
            key={dataset.id}
            style={{
              borderRadius: '16px',
              border: '1px solid #e5e5e7',
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
              transition: 'all 0.3s ease'
            }}
            bodyStyle={{ padding: '24px' }}
            hoverable
            actions={[
              <Tooltip title="Edit Dataset">
                <Button
                  type="text"
                  icon={<EditOutlined />}
                  onClick={() => handleEdit(dataset)}
                  style={{ color: '#007AFF' }}
                />
              </Tooltip>
            ]}
          >
            <div style={{ marginBottom: '16px' }}>
              <Title level={4} style={{
                margin: 0,
                marginBottom: '8px',
                color: '#1d1d1f',
                fontSize: '20px',
                fontWeight: 600,
                letterSpacing: '-0.2px'
              }}>
                {dataset.name}
              </Title>
              <Text style={{
                color: '#86868b',
                fontSize: '14px',
                lineHeight: '20px'
              }}>
                {dataset.description}
              </Text>
            </div>

            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text style={{ fontSize: '14px', color: '#86868b' }}>Source:</Text>
                <Text style={{ fontSize: '14px', fontWeight: 500, color: '#1d1d1f' }}>{dataset.source}</Text>
              </div>

              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text style={{ fontSize: '14px', color: '#86868b' }}>Classification:</Text>
                <Tag
                  color={getClassificationColor(dataset.privacyClassification)}
                  style={{
                    borderRadius: '8px',
                    fontSize: '12px',
                    fontWeight: 500,
                    textTransform: 'capitalize',
                    border: 'none'
                  }}
                >
                  {dataset.privacyClassification.replace('-', ' ')}
                </Tag>
              </div>

              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text style={{ fontSize: '14px', color: '#86868b' }}>Status:</Text>
                <Tag
                  color={getStatusColor(dataset.complianceStatus)}
                  style={{
                    borderRadius: '8px',
                    fontSize: '12px',
                    fontWeight: 500,
                    textTransform: 'capitalize',
                    border: 'none'
                  }}
                >
                  {dataset.complianceStatus.replace('-', ' ')}
                </Tag>
              </div>

              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text style={{ fontSize: '14px', color: '#86868b' }}>Collection Date:</Text>
                <Text style={{ fontSize: '14px', fontWeight: 500, color: '#1d1d1f' }}>
                  {formatDate(dataset.collectionDate)}
                </Text>
              </div>

              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text style={{ fontSize: '14px', color: '#86868b' }}>Owner:</Text>
                <Text style={{ fontSize: '14px', fontWeight: 500, color: '#1d1d1f' }}>{dataset.owner}</Text>
              </div>

              {dataset.tags.length > 0 && (
                <div style={{ marginTop: '12px' }}>
                  <Space size={[4, 4]} wrap>
                    {dataset.tags.map(tag => (
                      <Tag
                        key={tag}
                        style={{
                          borderRadius: '6px',
                          fontSize: '11px',
                          background: '#f5f5f7',
                          color: '#86868b',
                          border: 'none'
                        }}
                      >
                        {tag}
                      </Tag>
                    ))}
                  </Space>
                </div>
              )}
            </Space>
          </Card>
        ))}
      </div>

      {/* Add/Edit Dataset Modal */}
      <Modal
        title={
          <span style={{
            fontSize: '20px',
            fontWeight: 600,
            color: '#1d1d1f'
          }}>
            {editingDataset ? 'Edit Dataset' : 'Add New Dataset'}
          </span>
        }
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
        style={{ borderRadius: '16px' }}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          style={{ marginTop: '24px' }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label={<span style={{ fontWeight: 500, color: '#1d1d1f' }}>Dataset Name</span>}
                rules={[{ required: true, message: 'Please enter dataset name' }]}
              >
                <Input
                  placeholder="Enter dataset name"
                  style={{ borderRadius: '8px', height: '40px' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="source"
                label={<span style={{ fontWeight: 500, color: '#1d1d1f' }}>Data Source</span>}
                rules={[{ required: true, message: 'Please enter data source' }]}
              >
                <Input
                  placeholder="Enter data source"
                  style={{ borderRadius: '8px', height: '40px' }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label={<span style={{ fontWeight: 500, color: '#1d1d1f' }}>Description</span>}
            rules={[{ required: true, message: 'Please enter description' }]}
          >
            <TextArea
              rows={3}
              placeholder="Enter dataset description"
              style={{ borderRadius: '8px' }}
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="collectionDate"
                label={<span style={{ fontWeight: 500, color: '#1d1d1f' }}>Collection Date</span>}
                rules={[{ required: true, message: 'Please select collection date' }]}
              >
                <DatePicker
                  style={{ width: '100%', borderRadius: '8px', height: '40px' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="owner"
                label={<span style={{ fontWeight: 500, color: '#1d1d1f' }}>Data Owner</span>}
                rules={[{ required: true, message: 'Please enter data owner' }]}
              >
                <Input
                  placeholder="Team or person responsible"
                  style={{ borderRadius: '8px', height: '40px' }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="privacyClassification"
                label={<span style={{ fontWeight: 500, color: '#1d1d1f' }}>Privacy Classification</span>}
                rules={[{ required: true, message: 'Please select classification' }]}
              >
                <Select
                  placeholder="Select classification"
                  style={{ borderRadius: '8px' }}
                >
                  <Option value="public">Public</Option>
                  <Option value="internal">Internal</Option>
                  <Option value="confidential">Confidential</Option>
                  <Option value="restricted">Restricted</Option>
                  <Option value="highly-sensitive">Highly Sensitive</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="complianceStatus"
                label={<span style={{ fontWeight: 500, color: '#1d1d1f' }}>Compliance Status</span>}
                rules={[{ required: true, message: 'Please select status' }]}
              >
                <Select
                  placeholder="Select status"
                  style={{ borderRadius: '8px' }}
                >
                  <Option value="compliant">Compliant</Option>
                  <Option value="non-compliant">Non-Compliant</Option>
                  <Option value="under-review">Under Review</Option>
                  <Option value="requires-attention">Requires Attention</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="consentStatus"
                label={<span style={{ fontWeight: 500, color: '#1d1d1f' }}>Has Consent</span>}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="retentionPeriod"
                label={<span style={{ fontWeight: 500, color: '#1d1d1f' }}>Retention Period (days)</span>}
                rules={[{ required: true, message: 'Please enter retention period' }]}
              >
                <Input
                  type="number"
                  placeholder="365"
                  style={{ borderRadius: '8px', height: '40px' }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="tags"
            label={<span style={{ fontWeight: 500, color: '#1d1d1f' }}>Tags</span>}
          >
            <Input
              placeholder="Enter tags separated by commas"
              style={{ borderRadius: '8px', height: '40px' }}
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right', marginTop: '24px' }}>
            <Space>
              <Button
                onClick={() => setModalVisible(false)}
                style={{ borderRadius: '8px', height: '40px' }}
              >
                Cancel
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                style={{
                  borderRadius: '8px',
                  height: '40px',
                  fontWeight: 500
                }}
              >
                {editingDataset ? 'Update' : 'Create'} Dataset
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

// Enhanced Privacy Reviews Component with Step-by-Step Workflow
const PrivacyReviews: React.FC<{
  reviews: PrivacyReview[];
  datasets: Dataset[];
  onAddReview: (review: Omit<PrivacyReview, 'id' | 'createdAt'>) => void;
  onUpdateReview: (id: string, review: Partial<PrivacyReview>) => void;
}> = ({ reviews, datasets, onAddReview, onUpdateReview }) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [editingReview, setEditingReview] = useState<PrivacyReview | null>(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [form] = Form.useForm();

  const reviewSteps = [
    { title: 'Basic Information', description: 'Review details and scope' },
    { title: 'Risk Assessment', description: 'Evaluate privacy risks and impacts' },
    { title: 'Findings & Actions', description: 'Document findings and recommendations' },
    { title: 'Approval', description: 'Review approval and sign-off' }
  ];

  const handleAdd = () => {
    setEditingReview(null);
    setCurrentStep(0);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (review: PrivacyReview) => {
    setEditingReview(review);
    setCurrentStep(0);
    form.setFieldsValue({
      ...review,
      reviewDate: dayjs(review.reviewDate),
      findings: review.findings.join('\n'),
      recommendations: review.recommendations.join('\n')
    });
    setModalVisible(true);
  };

  const handleNext = () => {
    form.validateFields().then(() => {
      setCurrentStep(currentStep + 1);
    }).catch(() => {
      message.error('Please fill in all required fields');
    });
  };

  const handleSubmit = async (values: any) => {
    try {
      const reviewData = {
        datasetId: values.datasetId,
        reviewType: values.reviewType,
        status: values.status,
        riskLevel: values.riskLevel,
        reviewer: values.reviewer,
        reviewDate: values.reviewDate.format('YYYY-MM-DD'),
        findings: values.findings ? values.findings.split('\n').filter((f: string) => f.trim()) : [],
        recommendations: values.recommendations ? values.recommendations.split('\n').filter((r: string) => r.trim()) : [],
        approvalStatus: values.approvalStatus
      };

      if (editingReview) {
        onUpdateReview(editingReview.id, reviewData);
        message.success('Review updated successfully');
      } else {
        onAddReview(reviewData);
        message.success('Review created successfully');
      }

      setModalVisible(false);
      form.resetFields();
      setCurrentStep(0);
    } catch (error) {
      message.error('Failed to save review');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return '#34C759';
      case 'in-progress': return '#007AFF';
      case 'pending': return '#FF9500';
      case 'requires-action': return '#FF3B30';
      default: return '#8E8E93';
    }
  };

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return '#34C759';
      case 'medium': return '#FF9500';
      case 'high': return '#FF3B30';
      case 'very-high': return '#AF52DE';
      default: return '#8E8E93';
    }
  };

  const getDatasetName = (datasetId: string) => {
    const dataset = datasets.find(d => d.id === datasetId);
    return dataset?.name || 'Unknown Dataset';
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="datasetId"
                  label={<span style={{ fontWeight: 500, color: '#1d1d1f' }}>Dataset</span>}
                  rules={[{ required: true, message: 'Please select a dataset' }]}
                >
                  <Select placeholder="Select dataset to review" style={{ borderRadius: '8px' }}>
                    {datasets.map(dataset => (
                      <Option key={dataset.id} value={dataset.id}>
                        {dataset.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="reviewType"
                  label={<span style={{ fontWeight: 500, color: '#1d1d1f' }}>Review Type</span>}
                  rules={[{ required: true, message: 'Please select review type' }]}
                >
                  <Select style={{ borderRadius: '8px' }}>
                    <Option value="initial">Initial</Option>
                    <Option value="periodic">Periodic</Option>
                    <Option value="incident-driven">Incident-Driven</Option>
                    <Option value="regulatory">Regulatory</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="reviewDate"
                  label={<span style={{ fontWeight: 500, color: '#1d1d1f' }}>Review Date</span>}
                  rules={[{ required: true, message: 'Please select review date' }]}
                >
                  <DatePicker style={{ width: '100%', borderRadius: '8px', height: '40px' }} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="reviewer"
                  label={<span style={{ fontWeight: 500, color: '#1d1d1f' }}>Reviewer</span>}
                  rules={[{ required: true, message: 'Please enter reviewer name' }]}
                >
                  <Input placeholder="Enter reviewer name" style={{ borderRadius: '8px', height: '40px' }} />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              name="status"
              label={<span style={{ fontWeight: 500, color: '#1d1d1f' }}>Review Status</span>}
              rules={[{ required: true, message: 'Please select status' }]}
            >
              <Select style={{ borderRadius: '8px' }}>
                <Option value="pending">Pending</Option>
                <Option value="in-progress">In Progress</Option>
                <Option value="completed">Completed</Option>
                <Option value="requires-action">Requires Action</Option>
              </Select>
            </Form.Item>
          </>
        );
      case 1:
        return (
          <>
            <Alert
              message="Risk Assessment"
              description="Evaluate the privacy risks and potential impacts of this dataset."
              type="info"
              showIcon
              style={{ marginBottom: '24px', borderRadius: '8px' }}
            />
            <Form.Item
              name="riskLevel"
              label={<span style={{ fontWeight: 500, color: '#1d1d1f' }}>Risk Level</span>}
              rules={[{ required: true, message: 'Please assess risk level' }]}
            >
              <Select style={{ borderRadius: '8px' }}>
                <Option value="low">Low</Option>
                <Option value="medium">Medium</Option>
                <Option value="high">High</Option>
                <Option value="very-high">Very High</Option>
              </Select>
            </Form.Item>
          </>
        );
      case 2:
        return (
          <>
            <Alert
              message="Document Findings"
              description="Record any issues, concerns, or observations from your privacy review."
              type="info"
              showIcon
              style={{ marginBottom: '24px', borderRadius: '8px' }}
            />
            <Form.Item
              name="findings"
              label={<span style={{ fontWeight: 500, color: '#1d1d1f' }}>Findings</span>}
            >
              <TextArea
                rows={4}
                placeholder="Enter each finding on a new line..."
                style={{ borderRadius: '8px' }}
              />
            </Form.Item>
            <Form.Item
              name="recommendations"
              label={<span style={{ fontWeight: 500, color: '#1d1d1f' }}>Recommendations</span>}
              rules={[{ required: true, message: 'Please provide recommendations' }]}
            >
              <TextArea
                rows={4}
                placeholder="Enter each recommendation on a new line..."
                style={{ borderRadius: '8px' }}
              />
            </Form.Item>
          </>
        );
      case 3:
        return (
          <>
            <Alert
              message="Review Approval"
              description="Final approval and sign-off for this privacy review."
              type="success"
              showIcon
              style={{ marginBottom: '24px', borderRadius: '8px' }}
            />
            <Form.Item
              name="approvalStatus"
              label={<span style={{ fontWeight: 500, color: '#1d1d1f' }}>Approval Status</span>}
              rules={[{ required: true, message: 'Please select approval status' }]}
            >
              <Select style={{ borderRadius: '8px' }}>
                <Option value="pending">Pending</Option>
                <Option value="approved">Approved</Option>
                <Option value="rejected">Rejected</Option>
              </Select>
            </Form.Item>
          </>
        );
      default:
        return null;
    }
  };

  return (
    <div style={{ padding: '0 4px' }}>
      <div style={{ marginBottom: '32px' }}>
        <Title level={1} style={{
          color: '#1d1d1f',
          fontSize: '34px',
          fontWeight: 700,
          margin: 0,
          marginBottom: '8px',
          letterSpacing: '-0.5px'
        }}>
          Privacy Reviews
        </Title>
        <Text style={{
          color: '#86868b',
          fontSize: '17px',
          fontWeight: 400,
          lineHeight: '22px'
        }}>
          Manage privacy impact assessments and compliance reviews
        </Text>
      </div>

      <div style={{ marginBottom: '24px' }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAdd}
          size="large"
          style={{
            borderRadius: '12px',
            height: '48px',
            fontSize: '16px',
            fontWeight: 500,
            boxShadow: '0 4px 12px rgba(0, 122, 255, 0.3)'
          }}
        >
          New Privacy Review
        </Button>
      </div>

      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(400px, 1fr))',
        gap: '20px'
      }}>
        {reviews.map((review) => (
          <Card
            key={review.id}
            style={{
              borderRadius: '16px',
              border: '1px solid #e5e5e7',
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
              transition: 'all 0.3s ease'
            }}
            bodyStyle={{ padding: '24px' }}
            hoverable
            actions={[
              <Tooltip title="Edit Review">
                <Button
                  type="text"
                  icon={<EditOutlined />}
                  onClick={() => handleEdit(review)}
                  style={{ color: '#007AFF' }}
                />
              </Tooltip>
            ]}
          >
            <div style={{ marginBottom: '16px' }}>
              <Title level={4} style={{
                margin: 0,
                marginBottom: '8px',
                color: '#1d1d1f',
                fontSize: '20px',
                fontWeight: 600,
                letterSpacing: '-0.2px'
              }}>
                {getDatasetName(review.datasetId)} Review
              </Title>
              <Text style={{
                color: '#86868b',
                fontSize: '14px',
                lineHeight: '20px',
                textTransform: 'capitalize'
              }}>
                {review.reviewType.replace('-', ' ')} review
              </Text>
            </div>

            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text style={{ fontSize: '14px', color: '#86868b' }}>Status:</Text>
                <Tag
                  color={getStatusColor(review.status)}
                  style={{
                    borderRadius: '8px',
                    fontSize: '12px',
                    fontWeight: 500,
                    textTransform: 'capitalize',
                    border: 'none'
                  }}
                >
                  {review.status.replace('-', ' ')}
                </Tag>
              </div>

              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text style={{ fontSize: '14px', color: '#86868b' }}>Risk Level:</Text>
                <Tag
                  color={getRiskColor(review.riskLevel)}
                  style={{
                    borderRadius: '8px',
                    fontSize: '12px',
                    fontWeight: 500,
                    textTransform: 'capitalize',
                    border: 'none'
                  }}
                >
                  {review.riskLevel.replace('-', ' ')}
                </Tag>
              </div>

              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text style={{ fontSize: '14px', color: '#86868b' }}>Reviewer:</Text>
                <Text style={{ fontSize: '14px', fontWeight: 500, color: '#1d1d1f' }}>{review.reviewer}</Text>
              </div>

              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text style={{ fontSize: '14px', color: '#86868b' }}>Review Date:</Text>
                <Text style={{ fontSize: '14px', fontWeight: 500, color: '#1d1d1f' }}>
                  {formatDate(review.reviewDate)}
                </Text>
              </div>

              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text style={{ fontSize: '14px', color: '#86868b' }}>Approval:</Text>
                <Tag
                  color={review.approvalStatus === 'approved' ? '#34C759' :
                        review.approvalStatus === 'rejected' ? '#FF3B30' : '#FF9500'}
                  style={{
                    borderRadius: '8px',
                    fontSize: '12px',
                    fontWeight: 500,
                    textTransform: 'capitalize',
                    border: 'none'
                  }}
                >
                  {review.approvalStatus}
                </Tag>
              </div>
            </Space>
          </Card>
        ))}
      </div>

      {/* Privacy Review Modal with Steps */}
      <Modal
        title={
          <div style={{ marginBottom: '16px' }}>
            <span style={{
              fontSize: '20px',
              fontWeight: 600,
              color: '#1d1d1f'
            }}>
              {editingReview ? 'Edit Privacy Review' : 'New Privacy Review'}
            </span>
          </div>
        }
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setCurrentStep(0);
        }}
        footer={null}
        width={700}
        style={{ borderRadius: '16px' }}
      >
        <Steps current={currentStep} style={{ marginBottom: '32px' }}>
          {reviewSteps.map((step, index) => (
            <Step key={index} title={step.title} description={step.description} />
          ))}
        </Steps>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            reviewType: 'periodic',
            status: 'pending',
            riskLevel: 'medium',
            approvalStatus: 'pending'
          }}
        >
          {renderStepContent()}

          <Form.Item style={{ marginBottom: 0, textAlign: 'right', marginTop: '32px' }}>
            <Space>
              {currentStep > 0 && (
                <Button
                  onClick={() => setCurrentStep(currentStep - 1)}
                  style={{ borderRadius: '8px', height: '40px' }}
                >
                  Previous
                </Button>
              )}
              <Button
                onClick={() => {
                  setModalVisible(false);
                  setCurrentStep(0);
                }}
                style={{ borderRadius: '8px', height: '40px' }}
              >
                Cancel
              </Button>
              {currentStep < reviewSteps.length - 1 ? (
                <Button
                  type="primary"
                  onClick={handleNext}
                  style={{
                    borderRadius: '8px',
                    height: '40px',
                    fontWeight: 500
                  }}
                >
                  Next
                </Button>
              ) : (
                <Button
                  type="primary"
                  htmlType="submit"
                  style={{
                    borderRadius: '8px',
                    height: '40px',
                    fontWeight: 500
                  }}
                >
                  {editingReview ? 'Update' : 'Create'} Review
                </Button>
              )}
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

// Enhanced Reports Component
const Reports: React.FC<{ datasets: Dataset[]; reviews: PrivacyReview[] }> = ({ datasets, reviews }) => {
  const sampleReports = [
    {
      id: '1',
      title: 'Q2 2024 GDPR Compliance Report',
      type: 'GDPR',
      status: 'Final',
      generated: '2024-07-01',
      period: '2024-04-01 to 2024-06-30'
    },
    {
      id: '2',
      title: 'Privacy Impact Assessment Summary',
      type: 'PIA',
      status: 'Draft',
      generated: '2024-07-10',
      period: '2024-01-01 to 2024-06-30'
    }
  ];

  return (
    <div style={{ padding: '0 4px' }}>
      <div style={{ marginBottom: '32px' }}>
        <Title level={1} style={{
          color: '#1d1d1f',
          fontSize: '34px',
          fontWeight: 700,
          margin: 0,
          marginBottom: '8px',
          letterSpacing: '-0.5px'
        }}>
          Reports & Analytics
        </Title>
        <Text style={{
          color: '#86868b',
          fontSize: '17px',
          fontWeight: 400,
          lineHeight: '22px'
        }}>
          Generate compliance reports and analyze data governance metrics
        </Text>
      </div>

      <div style={{ marginBottom: '24px' }}>
        <Button
          type="primary"
          icon={<FileTextOutlined />}
          size="large"
          style={{
            borderRadius: '12px',
            height: '48px',
            fontSize: '16px',
            fontWeight: 500,
            boxShadow: '0 4px 12px rgba(0, 122, 255, 0.3)'
          }}
        >
          Generate New Report
        </Button>
      </div>

      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(400px, 1fr))',
        gap: '20px'
      }}>
        {sampleReports.map((report) => (
          <Card
            key={report.id}
            style={{
              borderRadius: '16px',
              border: '1px solid #e5e5e7',
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
              transition: 'all 0.3s ease'
            }}
            bodyStyle={{ padding: '24px' }}
            hoverable
            actions={[
              <Tooltip title="Download Report">
                <Button
                  type="text"
                  icon={<DownloadOutlined />}
                  style={{ color: '#007AFF' }}
                />
              </Tooltip>
            ]}
          >
            <div style={{ marginBottom: '16px' }}>
              <Title level={4} style={{
                margin: 0,
                marginBottom: '8px',
                color: '#1d1d1f',
                fontSize: '20px',
                fontWeight: 600,
                letterSpacing: '-0.2px'
              }}>
                {report.title}
              </Title>
              <Tag
                color="#007AFF"
                style={{
                  borderRadius: '8px',
                  fontSize: '12px',
                  fontWeight: 500,
                  border: 'none'
                }}
              >
                {report.type}
              </Tag>
            </div>

            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text style={{ fontSize: '14px', color: '#86868b' }}>Status:</Text>
                <Tag
                  color={report.status === 'Final' ? '#34C759' : '#FF9500'}
                  style={{
                    borderRadius: '8px',
                    fontSize: '12px',
                    fontWeight: 500,
                    border: 'none'
                  }}
                >
                  {report.status}
                </Tag>
              </div>

              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text style={{ fontSize: '14px', color: '#86868b' }}>Generated:</Text>
                <Text style={{ fontSize: '14px', fontWeight: 500, color: '#1d1d1f' }}>
                  {formatDate(report.generated)}
                </Text>
              </div>

              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text style={{ fontSize: '14px', color: '#86868b' }}>Period:</Text>
                <Text style={{ fontSize: '14px', fontWeight: 500, color: '#1d1d1f' }}>{report.period}</Text>
              </div>
            </Space>
          </Card>
        ))}
      </div>
    </div>
  );
};

const App: React.FC = () => {
  const [currentPage, setCurrentPage] = useState('/');
  const [datasets, setDatasets] = useState<Dataset[]>([]);
  const [reviews, setReviews] = useState<PrivacyReview[]>([]);

  // Initialize with sample data
  useEffect(() => {
    const sampleDatasets: Dataset[] = [
      {
        id: '1',
        name: 'Customer Purchase History',
        description: 'Historical transaction data from e-commerce platform including purchase patterns and preferences',
        source: 'E-commerce Platform API',
        collectionDate: '2024-01-15',
        privacyClassification: 'confidential',
        complianceStatus: 'compliant',
        owner: 'Data Engineering Team',
        tags: ['e-commerce', 'financial', 'customer-data'],
        consentStatus: true,
        retentionPeriod: 2555,
        createdAt: '2024-01-15',
        updatedAt: '2024-06-15'
      },
      {
        id: '2',
        name: 'User Behavior Analytics',
        description: 'Website and mobile app user interaction data for product optimization',
        source: 'Google Analytics & Mobile SDK',
        collectionDate: '2024-02-01',
        privacyClassification: 'internal',
        complianceStatus: 'under-review',
        owner: 'Product Analytics Team',
        tags: ['analytics', 'behavioral', 'product'],
        consentStatus: true,
        retentionPeriod: 730,
        createdAt: '2024-02-01',
        updatedAt: '2024-05-01'
      },
      {
        id: '3',
        name: 'Employee HR Records',
        description: 'Comprehensive employee data including personal information, performance, and compensation',
        source: 'HRIS System',
        collectionDate: '2024-01-01',
        privacyClassification: 'highly-sensitive',
        complianceStatus: 'compliant',
        owner: 'Human Resources',
        tags: ['hr', 'employee', 'sensitive', 'pii'],
        consentStatus: true,
        retentionPeriod: 2555,
        createdAt: '2024-01-01',
        updatedAt: '2024-07-01'
      }
    ];

    const sampleReviews: PrivacyReview[] = [
      {
        id: '1',
        datasetId: '1',
        reviewType: 'periodic',
        status: 'completed',
        riskLevel: 'medium',
        reviewer: 'Jane Smith, Privacy Officer',
        reviewDate: '2024-06-15',
        findings: ['Marketing consent mechanism could be more granular'],
        recommendations: [
          'Enhance consent granularity for marketing purposes',
          'Implement automated data retention cleanup',
          'Review third-party data sharing agreements'
        ],
        approvalStatus: 'approved',
        createdAt: '2024-06-15'
      },
      {
        id: '2',
        datasetId: '2',
        reviewType: 'initial',
        status: 'in-progress',
        riskLevel: 'low',
        reviewer: 'John Doe, Compliance Manager',
        reviewDate: '2024-07-01',
        findings: [],
        recommendations: [],
        approvalStatus: 'pending',
        createdAt: '2024-07-01'
      }
    ];

    setDatasets(sampleDatasets);
    setReviews(sampleReviews);
  }, []);

  const handleAddDataset = (datasetData: Omit<Dataset, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newDataset: Dataset = {
      ...datasetData,
      id: generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    setDatasets(prev => [...prev, newDataset]);
  };

  const handleEditDataset = (id: string, datasetData: Partial<Dataset>) => {
    setDatasets(prev => prev.map(dataset =>
      dataset.id === id
        ? { ...dataset, ...datasetData, updatedAt: new Date().toISOString() }
        : dataset
    ));
  };

  const handleAddReview = (reviewData: Omit<PrivacyReview, 'id' | 'createdAt'>) => {
    const newReview: PrivacyReview = {
      ...reviewData,
      id: generateId(),
      createdAt: new Date().toISOString()
    };
    setReviews(prev => [...prev, newReview]);
  };

  const handleUpdateReview = (id: string, reviewData: Partial<PrivacyReview>) => {
    setReviews(prev => prev.map(review =>
      review.id === id ? { ...review, ...reviewData } : review
    ));
  };

  const theme = {
    token: {
      colorPrimary: '#007AFF',
      colorSuccess: '#34C759',
      colorWarning: '#FF9500',
      colorError: '#FF3B30',
      colorInfo: '#007AFF',
      borderRadius: 8,
      fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", "SF Pro Text", Inter, "Segoe UI", Roboto, sans-serif',
    },
    components: {
      Button: {
        borderRadius: 8,
        fontWeight: 500,
      },
      Card: {
        borderRadius: 16,
      },
      Modal: {
        borderRadius: 16,
      },
      Input: {
        borderRadius: 8,
      },
      Select: {
        borderRadius: 8,
      },
    },
  };

  const menuItems = [
    { key: '/', icon: <DashboardOutlined />, label: 'Dashboard' },
    { key: '/datasets', icon: <DatabaseOutlined />, label: 'Dataset Management' },
    { key: '/privacy-reviews', icon: <SafetyOutlined />, label: 'Privacy Reviews' },
    { key: '/reports', icon: <FileTextOutlined />, label: 'Reports & Analytics' },
  ];

  const renderContent = () => {
    switch (currentPage) {
      case '/datasets':
        return (
          <DatasetManagement
            datasets={datasets}
            onAddDataset={handleAddDataset}
            onEditDataset={handleEditDataset}
          />
        );
      case '/privacy-reviews':
        return (
          <PrivacyReviews
            reviews={reviews}
            datasets={datasets}
            onAddReview={handleAddReview}
            onUpdateReview={handleUpdateReview}
          />
        );
      case '/reports':
        return <Reports datasets={datasets} reviews={reviews} />;
      default:
        return <Dashboard datasets={datasets} reviews={reviews} />;
    }
  };

  return (
    <ConfigProvider theme={theme}>
      <Layout style={{ minHeight: '100vh', background: '#f8f9fa' }}>
        {/* Authentic Apple-style Header */}
        <Header style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          background: 'rgba(251, 251, 253, 0.94)', // Apple's signature light background
          backdropFilter: 'saturate(180%) blur(20px)',
          borderBottom: '1px solid rgba(0, 0, 0, 0.12)',
          boxShadow: '0 1px 0 rgba(255, 255, 255, 0.44) inset, 0 1px 3px rgba(0, 0, 0, 0.12)',
          padding: '0 32px',
          height: '72px',
          position: 'sticky',
          top: 0,
          zIndex: 1000
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            {/* Apple Logo */}
            <div style={{
              marginRight: '12px',
              display: 'flex',
              alignItems: 'center'
            }}>
              <img
                src="https://upload.wikimedia.org/wikipedia/commons/f/fa/Apple_logo_black.svg"
                alt="Apple Logo"
                style={{
                  width: '28px',
                  height: '34px',
                  opacity: 0.9
                }}
              />
            </div>
            <div>
              <Title level={3} style={{
                margin: 0,
                color: '#1d1d1f',
                fontWeight: 700,
                fontSize: '22px',
                letterSpacing: '-0.3px',
                lineHeight: '26px'
              }}>
                Privacy Demo
              </Title>
              <Text style={{
                color: '#86868b',
                fontSize: '14px',
                fontWeight: 400,
                lineHeight: '18px'
              }}>
                Customer Data Catalogue Management
              </Text>
            </div>
          </div>

          <Space size="large">
            <Text style={{ color: '#86868b', fontSize: '15px', fontWeight: 500 }}>
              Privacy Officer
            </Text>
            <Avatar
              size={36}
              icon={<UserOutlined />}
              style={{
                backgroundColor: '#007AFF',
                border: '2px solid rgba(255, 255, 255, 0.8)',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)'
              }}
            />
          </Space>
        </Header>

        <Layout style={{ background: '#f8f9fa' }}>
          {/* Apple-style Sidebar */}
          <Sider
            width={280}
            style={{
              background: 'rgba(255, 255, 255, 0.85)',
              backdropFilter: 'saturate(180%) blur(20px)',
              borderRight: '1px solid rgba(0, 0, 0, 0.12)',
              boxShadow: '1px 0 0 rgba(255, 255, 255, 0.44) inset, 1px 0 3px rgba(0, 0, 0, 0.12)'
            }}
          >
            <div style={{ padding: '32px 24px 20px 24px' }}>
              <Text style={{
                color: '#86868b',
                fontSize: '13px',
                fontWeight: 600,
                textTransform: 'uppercase',
                letterSpacing: '0.8px'
              }}>
                Navigation
              </Text>
            </div>

            <Menu
              mode="inline"
              selectedKeys={[currentPage]}
              onClick={({ key }) => setCurrentPage(key)}
              style={{
                border: 'none',
                background: 'transparent',
                padding: '0 20px'
              }}
              items={menuItems.map(item => ({
                ...item,
                style: {
                  borderRadius: '12px',
                  margin: '4px 0',
                  height: '48px',
                  lineHeight: '48px',
                  fontWeight: 500,
                  fontSize: '16px',
                  color: currentPage === item.key ? '#007AFF' : '#1d1d1f'
                }
              }))}
            />

            <div style={{ padding: '32px 24px 24px 24px' }}>
              <Text style={{
                color: '#86868b',
                fontSize: '13px',
                fontWeight: 600,
                textTransform: 'uppercase',
                letterSpacing: '0.8px',
                marginBottom: '16px',
                display: 'block'
              }}>
                Quick Stats
              </Text>
              <div style={{
                padding: '20px',
                background: 'rgba(255, 255, 255, 0.6)',
                borderRadius: '16px',
                border: '1px solid rgba(0, 0, 0, 0.1)',
                backdropFilter: 'blur(10px)'
              }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '12px' }}>
                  <Text style={{ fontSize: '14px', color: '#86868b', fontWeight: 500 }}>Total Datasets</Text>
                  <Text style={{ fontSize: '14px', fontWeight: 700, color: '#1d1d1f' }}>{datasets.length}</Text>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '12px' }}>
                  <Text style={{ fontSize: '14px', color: '#86868b', fontWeight: 500 }}>Compliant</Text>
                  <Text style={{ fontSize: '14px', fontWeight: 700, color: '#34c759' }}>
                    {datasets.filter(d => d.complianceStatus === 'compliant').length}
                  </Text>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '12px' }}>
                  <Text style={{ fontSize: '14px', color: '#86868b', fontWeight: 500 }}>Under Review</Text>
                  <Text style={{ fontSize: '14px', fontWeight: 700, color: '#ff9500' }}>
                    {datasets.filter(d => d.complianceStatus === 'under-review').length}
                  </Text>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Text style={{ fontSize: '14px', color: '#86868b', fontWeight: 500 }}>Compliance Score</Text>
                  <Text style={{ fontSize: '14px', fontWeight: 700, color: '#007aff' }}>
                    {datasets.length > 0 ? Math.round((datasets.filter(d => d.complianceStatus === 'compliant').length / datasets.length) * 100) : 0}%
                  </Text>
                </div>
              </div>
            </div>
          </Sider>

          <Layout style={{ padding: '32px', background: 'rgba(248, 249, 250, 0.6)' }}>
            <Content
              style={{
                background: 'rgba(255, 255, 255, 0.9)',
                backdropFilter: 'saturate(180%) blur(20px)',
                borderRadius: '20px',
                padding: '40px',
                minHeight: 'calc(100vh - 136px)',
                boxShadow: '0 1px 0 rgba(255, 255, 255, 0.44) inset, 0 8px 32px rgba(0, 0, 0, 0.12)',
                border: '1px solid rgba(255, 255, 255, 0.18)'
              }}
            >
              {renderContent()}
            </Content>
          </Layout>
        </Layout>
      </Layout>
    </ConfigProvider>
  );
};

export default App;
