import React, { useEffect, Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, Spin } from 'antd';

// Lazy load components to help with debugging
const AppLayout = React.lazy(() => import('./components/Layout/AppLayout'));
const Dashboard = React.lazy(() => import('./pages/Dashboard'));
const DatasetManagement = React.lazy(() => import('./pages/DatasetManagement'));
const PrivacyReviews = React.lazy(() => import('./pages/PrivacyReviews'));
const Reports = React.lazy(() => import('./pages/Reports'));

const App: React.FC = () => {
  useEffect(() => {
    // Initialize storage with mock data on first load
    try {
      const { initializeStorage } = require('./utils/storage');
      const { mockDatasets, mockPrivacyReviews, mockComplianceReports } = require('./data/mockData');
      initializeStorage(mockDatasets, mockPrivacyReviews, mockComplianceReports);
    } catch (error) {
      console.error('Error initializing storage:', error);
    }
  }, []);

  const theme = {
    token: {
      colorPrimary: '#007AFF',
      colorSuccess: '#34C759',
      colorWarning: '#FF9500',
      colorError: '#FF3B30',
      colorInfo: '#007AFF',
      borderRadius: 8,
      fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", "Segoe UI", Roboto, sans-serif',
    },
    components: {
      Button: {
        borderRadius: 8,
        fontWeight: 500,
      },
      Card: {
        borderRadius: 12,
      },
      Table: {
        borderRadius: 12,
      },
    },
  };

  return (
    <ConfigProvider theme={theme}>
      <Router>
        <Suspense fallback={
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100vh',
            flexDirection: 'column',
            gap: '16px'
          }}>
            <Spin size="large" />
            <div style={{ color: '#86868b' }}>Loading Apple Privacy Demo...</div>
          </div>
        }>
          <AppLayout>
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/datasets" element={<DatasetManagement />} />
              <Route path="/privacy-reviews" element={<PrivacyReviews />} />
              <Route path="/reports" element={<Reports />} />
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </AppLayout>
        </Suspense>
      </Router>
    </ConfigProvider>
  );
};

export default App;
