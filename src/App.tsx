import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, Layout, Menu, Typography, Card, Button, Space } from 'antd';
import { DashboardOutlined, DatabaseOutlined, SafetyOutlined, FileTextOutlined, AppstoreOutlined } from '@ant-design/icons';

const { Header, Content, Sider } = Layout;
const { Title, Text } = Typography;

// Simple Dashboard Component
const Dashboard = () => (
  <div>
    <Title level={2} style={{ color: '#1d1d1f', marginBottom: '24px' }}>
      Privacy Dashboard
    </Title>
    <Space direction="vertical" size="large" style={{ width: '100%' }}>
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '16px' }}>
        <Card>
          <Title level={4} style={{ color: '#007AFF', margin: 0 }}>3</Title>
          <Text>Total Datasets</Text>
        </Card>
        <Card>
          <Title level={4} style={{ color: '#34C759', margin: 0 }}>2</Title>
          <Text>Compliant Datasets</Text>
        </Card>
        <Card>
          <Title level={4} style={{ color: '#FF9500', margin: 0 }}>1</Title>
          <Text>Pending Reviews</Text>
        </Card>
        <Card>
          <Title level={4} style={{ color: '#007AFF', margin: 0 }}>87%</Title>
          <Text>Compliance Score</Text>
        </Card>
      </div>
      <Card title="Recent Activities">
        <Space direction="vertical">
          <Text>✅ Privacy review completed for Customer Purchase History</Text>
          <Text>📊 New dataset registered: User Behavior Analytics</Text>
          <Text>⚠️ Consent withdrawal processed</Text>
          <Text>📋 Compliance report generated</Text>
        </Space>
      </Card>
    </Space>
  </div>
);

// Simple Dataset Management Component
const DatasetManagement = () => (
  <div>
    <Title level={2} style={{ color: '#1d1d1f', marginBottom: '24px' }}>
      Dataset Management
    </Title>
    <Space direction="vertical" size="large" style={{ width: '100%' }}>
      <Button type="primary" icon={<DatabaseOutlined />}>
        Add New Dataset
      </Button>
      <Card title="Customer Purchase History">
        <Space direction="vertical">
          <Text><strong>Source:</strong> E-commerce Platform API</Text>
          <Text><strong>Classification:</strong> <span style={{ color: '#FF9500' }}>Confidential</span></Text>
          <Text><strong>Status:</strong> <span style={{ color: '#34C759' }}>Compliant</span></Text>
          <Text><strong>Collection Date:</strong> 2024-01-15</Text>
        </Space>
      </Card>
      <Card title="User Behavior Analytics">
        <Space direction="vertical">
          <Text><strong>Source:</strong> Google Analytics & Mobile SDK</Text>
          <Text><strong>Classification:</strong> <span style={{ color: '#007AFF' }}>Internal</span></Text>
          <Text><strong>Status:</strong> <span style={{ color: '#FF9500' }}>Under Review</span></Text>
          <Text><strong>Collection Date:</strong> 2024-02-01</Text>
        </Space>
      </Card>
    </Space>
  </div>
);

// Simple Privacy Reviews Component
const PrivacyReviews = () => (
  <div>
    <Title level={2} style={{ color: '#1d1d1f', marginBottom: '24px' }}>
      Privacy Reviews
    </Title>
    <Space direction="vertical" size="large" style={{ width: '100%' }}>
      <Button type="primary" icon={<SafetyOutlined />}>
        New Privacy Review
      </Button>
      <Card title="Customer Purchase History Review">
        <Space direction="vertical">
          <Text><strong>Review Type:</strong> Periodic</Text>
          <Text><strong>Status:</strong> <span style={{ color: '#34C759' }}>Completed</span></Text>
          <Text><strong>Risk Level:</strong> <span style={{ color: '#FF9500' }}>Medium</span></Text>
          <Text><strong>Reviewer:</strong> Jane Smith, Privacy Officer</Text>
          <Text><strong>Review Date:</strong> 2024-06-15</Text>
        </Space>
      </Card>
    </Space>
  </div>
);

// Simple Reports Component
const Reports = () => (
  <div>
    <Title level={2} style={{ color: '#1d1d1f', marginBottom: '24px' }}>
      Reports & Analytics
    </Title>
    <Space direction="vertical" size="large" style={{ width: '100%' }}>
      <Button type="primary" icon={<FileTextOutlined />}>
        Generate Report
      </Button>
      <Card title="Q2 2024 GDPR Compliance Report">
        <Space direction="vertical">
          <Text><strong>Type:</strong> GDPR</Text>
          <Text><strong>Status:</strong> <span style={{ color: '#34C759' }}>Final</span></Text>
          <Text><strong>Generated:</strong> 2024-07-01</Text>
          <Text><strong>Period:</strong> 2024-04-01 to 2024-06-30</Text>
          <Button size="small">Download Report</Button>
        </Space>
      </Card>
    </Space>
  </div>
);

const App: React.FC = () => {
  const [currentPage, setCurrentPage] = React.useState('/');

  const theme = {
    token: {
      colorPrimary: '#007AFF',
      colorSuccess: '#34C759',
      colorWarning: '#FF9500',
      colorError: '#FF3B30',
      colorInfo: '#007AFF',
      borderRadius: 8,
      fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", "Segoe UI", Roboto, sans-serif',
    },
  };

  const menuItems = [
    { key: '/', icon: <DashboardOutlined />, label: 'Dashboard' },
    { key: '/datasets', icon: <DatabaseOutlined />, label: 'Dataset Management' },
    { key: '/privacy-reviews', icon: <SafetyOutlined />, label: 'Privacy Reviews' },
    { key: '/reports', icon: <FileTextOutlined />, label: 'Reports & Analytics' },
  ];

  const renderContent = () => {
    switch (currentPage) {
      case '/datasets': return <DatasetManagement />;
      case '/privacy-reviews': return <PrivacyReviews />;
      case '/reports': return <Reports />;
      default: return <Dashboard />;
    }
  };

  return (
    <ConfigProvider theme={theme}>
      <Layout style={{ minHeight: '100vh' }}>
        <Header style={{
          display: 'flex',
          alignItems: 'center',
          background: '#ffffff',
          borderBottom: '1px solid #f0f0f0',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)'
        }}>
          <AppstoreOutlined style={{ fontSize: '24px', color: '#1d1d1f', marginRight: '16px' }} />
          <div>
            <Title level={4} style={{ margin: 0, color: '#1d1d1f', fontSize: '18px' }}>
              Apple Privacy Demo
            </Title>
            <Text style={{ color: '#86868b', fontSize: '12px' }}>
              Customer Data Catalogue Management
            </Text>
          </div>
        </Header>

        <Layout>
          <Sider width={240} style={{ background: '#ffffff', borderRight: '1px solid #f0f0f0' }}>
            <div style={{ padding: '24px 16px 16px 16px' }}>
              <Text style={{ color: '#86868b', fontSize: '12px', fontWeight: 500, textTransform: 'uppercase' }}>
                Navigation
              </Text>
            </div>
            <Menu
              mode="inline"
              selectedKeys={[currentPage]}
              onClick={({ key }) => setCurrentPage(key)}
              style={{ border: 'none', background: 'transparent', padding: '0 16px' }}
              items={menuItems}
            />
          </Sider>

          <Layout style={{ padding: '24px' }}>
            <Content style={{
              background: '#ffffff',
              borderRadius: '12px',
              padding: '24px',
              minHeight: 'calc(100vh - 112px)',
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
              border: '1px solid #f0f0f0'
            }}>
              {renderContent()}
            </Content>
          </Layout>
        </Layout>
      </Layout>
    </ConfigProvider>
  );
};

export default App;
