import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import { initializeStorage } from './utils/storage';
import { mockDatasets, mockPrivacyReviews, mockComplianceReports } from './data/mockData';
import AppLayout from './components/Layout/AppLayout';
import Dashboard from './pages/Dashboard';
import DatasetManagement from './pages/DatasetManagement';
import PrivacyReviews from './pages/PrivacyReviews';
import Reports from './pages/Reports';

const App: React.FC = () => {
  useEffect(() => {
    // Initialize storage with mock data on first load
    try {
      initializeStorage(mockDatasets, mockPrivacyReviews, mockComplianceReports);
    } catch (error) {
      console.error('Error initializing storage:', error);
    }
  }, []);

  const theme = {
    token: {
      colorPrimary: '#007AFF',
      colorSuccess: '#34C759',
      colorWarning: '#FF9500',
      colorError: '#FF3B30',
      colorInfo: '#007AFF',
      borderRadius: 8,
      fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", "Segoe UI", Roboto, sans-serif',
    },
    components: {
      Button: {
        borderRadius: 8,
        fontWeight: 500,
      },
      Card: {
        borderRadius: 12,
      },
      Table: {
        borderRadius: 12,
      },
    },
  };

  return (
    <ConfigProvider theme={theme}>
      <Router>
        <AppLayout>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/datasets" element={<DatasetManagement />} />
            <Route path="/privacy-reviews" element={<PrivacyReviews />} />
            <Route path="/reports" element={<Reports />} />
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </AppLayout>
      </Router>
    </ConfigProvider>
  );
};

export default App;
