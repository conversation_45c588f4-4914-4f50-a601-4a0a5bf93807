import React from 'react';
import { Layout, Menu, Avatar, Space, Typography, Divider } from 'antd';
import {
  DashboardOutlined,
  DatabaseOutlined,
  SafetyOutlined,
  FileTextOutlined,
  UserOutlined,
  AppstoreOutlined
} from '@ant-design/icons';
import { useLocation, useNavigate } from 'react-router-dom';

const { Header, Content, Sider } = Layout;
const { Title, Text } = Typography;

interface AppLayoutProps {
  children: React.ReactNode;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();

  const menuItems = [
    {
      key: '/',
      icon: <DashboardOutlined />,
      label: 'Dashboard',
    },
    {
      key: '/datasets',
      icon: <DatabaseOutlined />,
      label: 'Dataset Management',
    },
    {
      key: '/privacy-reviews',
      icon: <SafetyOutlined />,
      label: 'Privacy Reviews',
    },
    {
      key: '/reports',
      icon: <FileTextOutlined />,
      label: 'Reports & Analytics',
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '0 24px',
        background: '#ffffff',
        borderBottom: '1px solid #f0f0f0',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <AppstoreOutlined style={{
            fontSize: '24px',
            color: '#1d1d1f',
            marginRight: '8px'
          }} />
          <div>
            <Title level={4} style={{
              margin: 0,
              color: '#1d1d1f',
              fontWeight: 600,
              fontSize: '18px'
            }}>
              Apple Privacy Demo
            </Title>
            <Text style={{
              color: '#86868b',
              fontSize: '12px',
              fontWeight: 400
            }}>
              Customer Data Catalogue Management
            </Text>
          </div>
        </div>

        <Space size="middle">
          <Text style={{ color: '#86868b', fontSize: '14px' }}>
            Privacy Officer
          </Text>
          <Avatar
            size="small"
            icon={<UserOutlined />}
            style={{
              backgroundColor: '#007AFF',
              border: '2px solid #f0f0f0'
            }}
          />
        </Space>
      </Header>

      <Layout>
        <Sider
          width={240}
          style={{
            background: '#ffffff',
            borderRight: '1px solid #f0f0f0',
            boxShadow: '2px 0 8px rgba(0, 0, 0, 0.06)'
          }}
        >
          <div style={{ padding: '24px 16px 16px 16px' }}>
            <Text style={{
              color: '#86868b',
              fontSize: '12px',
              fontWeight: 500,
              textTransform: 'uppercase',
              letterSpacing: '0.5px'
            }}>
              Navigation
            </Text>
          </div>

          <Menu
            mode="inline"
            selectedKeys={[location.pathname]}
            onClick={handleMenuClick}
            style={{
              border: 'none',
              background: 'transparent',
              padding: '0 16px'
            }}
            items={menuItems.map(item => ({
              ...item,
              style: {
                borderRadius: '8px',
                margin: '4px 0',
                height: '44px',
                lineHeight: '44px',
                fontWeight: 500
              }
            }))}
          />

          <Divider style={{ margin: '24px 16px 16px 16px' }} />

          <div style={{ padding: '0 16px' }}>
            <Text style={{
              color: '#86868b',
              fontSize: '12px',
              fontWeight: 500,
              textTransform: 'uppercase',
              letterSpacing: '0.5px'
            }}>
              Quick Stats
            </Text>
            <div style={{ marginTop: '12px', padding: '12px', background: '#f8f9fa', borderRadius: '8px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                <Text style={{ fontSize: '12px', color: '#86868b' }}>Total Datasets</Text>
                <Text style={{ fontSize: '12px', fontWeight: 600, color: '#1d1d1f' }}>3</Text>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                <Text style={{ fontSize: '12px', color: '#86868b' }}>Compliant</Text>
                <Text style={{ fontSize: '12px', fontWeight: 600, color: '#34c759' }}>2</Text>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                <Text style={{ fontSize: '12px', color: '#86868b' }}>Under Review</Text>
                <Text style={{ fontSize: '12px', fontWeight: 600, color: '#ff9500' }}>1</Text>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Text style={{ fontSize: '12px', color: '#86868b' }}>Compliance Score</Text>
                <Text style={{ fontSize: '12px', fontWeight: 600, color: '#007aff' }}>87%</Text>
              </div>
            </div>
          </div>
        </Sider>

        <Layout style={{ padding: '24px' }}>
          <Content
            style={{
              background: '#ffffff',
              borderRadius: '12px',
              padding: '24px',
              minHeight: 'calc(100vh - 112px)',
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
              border: '1px solid #f0f0f0'
            }}
          >
            {children}
          </Content>
        </Layout>
      </Layout>
    </Layout>
  );
};

export default AppLayout;
