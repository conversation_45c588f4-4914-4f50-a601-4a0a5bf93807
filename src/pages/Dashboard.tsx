import React, { useEffect, useState } from 'react';
import { Row, Col, Card, Statistic, Progress, Typography, Space, Tag, List, Avatar } from 'antd';
import {
  DatabaseOutlined,
  ShieldCheckOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  TrophyOutlined,
  UserDeleteOutlined,
  SecurityScanOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import { PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';
import { datasetStorage, privacyReviewStorage } from '../utils/storage';
import { Dataset, PrivacyReview, DashboardMetrics } from '../types';
import { mockDashboardMetrics } from '../data/mockData';

const { Title, Text } = Typography;

const Dashboard: React.FC = () => {
  const [datasets, setDatasets] = useState<Dataset[]>([]);
  const [reviews, setReviews] = useState<PrivacyReview[]>([]);
  const [metrics, setMetrics] = useState<DashboardMetrics>(mockDashboardMetrics);

  useEffect(() => {
    const loadedDatasets = datasetStorage.getAll();
    const loadedReviews = privacyReviewStorage.getAll();
    setDatasets(loadedDatasets);
    setReviews(loadedReviews);

    // Calculate real metrics from data
    const compliantCount = loadedDatasets.filter(d => d.complianceStatus === 'compliant').length;
    const pendingReviewsCount = loadedReviews.filter(r => r.status === 'pending' || r.status === 'in-progress').length;
    const highRiskCount = loadedDatasets.filter(d => d.privacyClassification === 'highly-sensitive' || d.privacyClassification === 'restricted').length;
    
    setMetrics({
      ...mockDashboardMetrics,
      totalDatasets: loadedDatasets.length,
      compliantDatasets: compliantCount,
      pendingReviews: pendingReviewsCount,
      highRiskDatasets: highRiskCount,
      complianceScore: loadedDatasets.length > 0 ? Math.round((compliantCount / loadedDatasets.length) * 100) : 0
    });
  }, []);

  const complianceData = [
    { name: 'Compliant', value: metrics.compliantDatasets, color: '#34C759' },
    { name: 'Under Review', value: metrics.totalDatasets - metrics.compliantDatasets, color: '#FF9500' }
  ];

  const privacyClassificationData = datasets.reduce((acc, dataset) => {
    const existing = acc.find(item => item.name === dataset.privacyClassification);
    if (existing) {
      existing.value += 1;
    } else {
      acc.push({ name: dataset.privacyClassification, value: 1 });
    }
    return acc;
  }, [] as { name: string; value: number }[]);

  const monthlyTrend = [
    { month: 'Jan', datasets: 15, reviews: 8 },
    { month: 'Feb', datasets: 18, reviews: 12 },
    { month: 'Mar', datasets: 22, reviews: 15 },
    { month: 'Apr', datasets: 25, reviews: 18 },
    { month: 'May', datasets: 28, reviews: 20 },
    { month: 'Jun', datasets: 30, reviews: 22 }
  ];

  const recentActivities = [
    {
      title: 'Privacy review completed for Customer Purchase History',
      description: 'Review approved with minor recommendations',
      time: '2 hours ago',
      type: 'review',
      status: 'success'
    },
    {
      title: 'New dataset registered: User Behavior Analytics',
      description: 'Awaiting initial privacy assessment',
      time: '5 hours ago',
      type: 'dataset',
      status: 'info'
    },
    {
      title: 'Consent withdrawal processed',
      description: '3 data subject requests processed',
      time: '1 day ago',
      type: 'consent',
      status: 'warning'
    },
    {
      title: 'Compliance report generated',
      description: 'Q2 2024 GDPR compliance report ready',
      time: '2 days ago',
      type: 'report',
      status: 'success'
    }
  ];

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'review': return <ShieldCheckOutlined />;
      case 'dataset': return <DatabaseOutlined />;
      case 'consent': return <UserDeleteOutlined />;
      case 'report': return <TrophyOutlined />;
      default: return <CheckCircleOutlined />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return '#34C759';
      case 'warning': return '#FF9500';
      case 'info': return '#007AFF';
      default: return '#8E8E93';
    }
  };

  return (
    <div>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2} style={{ margin: 0, color: '#1d1d1f', fontWeight: 600 }}>
          Privacy Dashboard
        </Title>
        <Text style={{ color: '#86868b', fontSize: '16px' }}>
          Overview of your data governance and privacy compliance status
        </Text>
      </div>

      {/* Key Metrics */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Total Datasets"
              value={metrics.totalDatasets}
              prefix={<DatabaseOutlined style={{ color: '#007AFF' }} />}
              valueStyle={{ color: '#1d1d1f', fontWeight: 600 }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Compliant Datasets"
              value={metrics.compliantDatasets}
              prefix={<CheckCircleOutlined style={{ color: '#34C759' }} />}
              valueStyle={{ color: '#34C759', fontWeight: 600 }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Pending Reviews"
              value={metrics.pendingReviews}
              prefix={<ClockCircleOutlined style={{ color: '#FF9500' }} />}
              valueStyle={{ color: '#FF9500', fontWeight: 600 }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Compliance Score"
              value={metrics.complianceScore}
              suffix="%"
              prefix={<TrophyOutlined style={{ color: '#007AFF' }} />}
              valueStyle={{ color: '#007AFF', fontWeight: 600 }}
            />
            <Progress 
              percent={metrics.complianceScore} 
              showInfo={false} 
              strokeColor="#007AFF"
              style={{ marginTop: '8px' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* Compliance Overview */}
        <Col xs={24} lg={12}>
          <Card title="Compliance Status" style={{ height: '400px' }}>
            <ResponsiveContainer width="100%" height={250}>
              <PieChart>
                <Pie
                  data={complianceData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={100}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {complianceData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
            <div style={{ textAlign: 'center', marginTop: '16px' }}>
              <Space size="large">
                {complianceData.map((item, index) => (
                  <div key={index} style={{ textAlign: 'center' }}>
                    <div style={{ 
                      width: '12px', 
                      height: '12px', 
                      backgroundColor: item.color, 
                      borderRadius: '50%',
                      display: 'inline-block',
                      marginRight: '8px'
                    }} />
                    <Text style={{ fontSize: '14px', color: '#86868b' }}>
                      {item.name}: {item.value}
                    </Text>
                  </div>
                ))}
              </Space>
            </div>
          </Card>
        </Col>

        {/* Privacy Classification */}
        <Col xs={24} lg={12}>
          <Card title="Privacy Classification Distribution" style={{ height: '400px' }}>
            <ResponsiveContainer width="100%" height={250}>
              <BarChart data={privacyClassificationData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="value" fill="#007AFF" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </Card>
        </Col>

        {/* Monthly Trends */}
        <Col xs={24} lg={12}>
          <Card title="Monthly Trends" style={{ height: '400px' }}>
            <ResponsiveContainer width="100%" height={250}>
              <LineChart data={monthlyTrend}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="datasets" stroke="#007AFF" strokeWidth={2} />
                <Line type="monotone" dataKey="reviews" stroke="#34C759" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
            <div style={{ textAlign: 'center', marginTop: '16px' }}>
              <Space size="large">
                <span>
                  <div style={{ 
                    width: '12px', 
                    height: '2px', 
                    backgroundColor: '#007AFF',
                    display: 'inline-block',
                    marginRight: '8px'
                  }} />
                  <Text style={{ fontSize: '14px', color: '#86868b' }}>Datasets</Text>
                </span>
                <span>
                  <div style={{ 
                    width: '12px', 
                    height: '2px', 
                    backgroundColor: '#34C759',
                    display: 'inline-block',
                    marginRight: '8px'
                  }} />
                  <Text style={{ fontSize: '14px', color: '#86868b' }}>Reviews</Text>
                </span>
              </Space>
            </div>
          </Card>
        </Col>

        {/* Recent Activities */}
        <Col xs={24} lg={12}>
          <Card title="Recent Activities" style={{ height: '400px' }}>
            <List
              itemLayout="horizontal"
              dataSource={recentActivities}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Avatar 
                        icon={getActivityIcon(item.type)} 
                        style={{ backgroundColor: getStatusColor(item.status) }}
                        size="small"
                      />
                    }
                    title={
                      <Text style={{ fontSize: '14px', fontWeight: 500, color: '#1d1d1f' }}>
                        {item.title}
                      </Text>
                    }
                    description={
                      <div>
                        <Text style={{ fontSize: '12px', color: '#86868b' }}>
                          {item.description}
                        </Text>
                        <br />
                        <Text style={{ fontSize: '11px', color: '#8e8e93' }}>
                          {item.time}
                        </Text>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
