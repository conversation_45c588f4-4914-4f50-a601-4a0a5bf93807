import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Button, 
  Space, 
  Tag, 
  Typography, 
  Input, 
  Select, 
  Card, 
  Modal, 
  Form, 
  DatePicker, 
  Switch,
  Row,
  Col,
  Divider,
  Tooltip,
  message
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
  FilterOutlined,
  ExportOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { Dataset, PrivacyClassification, ComplianceStatus } from '../types';
import { datasetStorage, generateId, formatDate, calculateDaysUntilExpiry } from '../utils/storage';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const DatasetManagement: React.FC = () => {
  const [datasets, setDatasets] = useState<Dataset[]>([]);
  const [filteredDatasets, setFilteredDatasets] = useState<Dataset[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingDataset, setEditingDataset] = useState<Dataset | null>(null);
  const [form] = Form.useForm();
  const [searchText, setSearchText] = useState('');
  const [filterStatus, setFilterStatus] = useState<ComplianceStatus | 'all'>('all');
  const [filterClassification, setFilterClassification] = useState<PrivacyClassification | 'all'>('all');

  useEffect(() => {
    loadDatasets();
  }, []);

  useEffect(() => {
    filterDatasets();
  }, [datasets, searchText, filterStatus, filterClassification]);

  const loadDatasets = () => {
    setLoading(true);
    const loadedDatasets = datasetStorage.getAll();
    setDatasets(loadedDatasets);
    setLoading(false);
  };

  const filterDatasets = () => {
    let filtered = datasets;

    if (searchText) {
      filtered = filtered.filter(dataset =>
        dataset.name.toLowerCase().includes(searchText.toLowerCase()) ||
        dataset.description.toLowerCase().includes(searchText.toLowerCase()) ||
        dataset.source.toLowerCase().includes(searchText.toLowerCase())
      );
    }

    if (filterStatus !== 'all') {
      filtered = filtered.filter(dataset => dataset.complianceStatus === filterStatus);
    }

    if (filterClassification !== 'all') {
      filtered = filtered.filter(dataset => dataset.privacyClassification === filterClassification);
    }

    setFilteredDatasets(filtered);
  };

  const handleAdd = () => {
    setEditingDataset(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (dataset: Dataset) => {
    setEditingDataset(dataset);
    form.setFieldsValue({
      ...dataset,
      collectionDate: dayjs(dataset.collectionDate),
      retentionExpirationDate: dayjs(dataset.retentionPolicy.expirationDate),
      consentDate: dataset.consentStatus.consentDate ? dayjs(dataset.consentStatus.consentDate) : null,
      consentExpiry: dataset.consentStatus.consentExpiry ? dayjs(dataset.consentStatus.consentExpiry) : null,
    });
    setModalVisible(true);
  };

  const handleDelete = (id: string) => {
    Modal.confirm({
      title: 'Delete Dataset',
      content: 'Are you sure you want to delete this dataset? This action cannot be undone.',
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: () => {
        datasetStorage.delete(id);
        loadDatasets();
        message.success('Dataset deleted successfully');
      },
    });
  };

  const handleSubmit = async (values: any) => {
    try {
      const dataset: Dataset = {
        id: editingDataset?.id || generateId(),
        name: values.name,
        description: values.description,
        source: values.source,
        collectionDate: values.collectionDate.format('YYYY-MM-DD'),
        collectionFrequency: values.collectionFrequency,
        dataTypes: [], // Simplified for demo
        schema: [], // Simplified for demo
        consentStatus: {
          hasConsent: values.hasConsent,
          consentType: values.consentType,
          consentDate: values.consentDate?.format('YYYY-MM-DD'),
          consentExpiry: values.consentExpiry?.format('YYYY-MM-DD'),
          withdrawalMechanism: values.withdrawalMechanism,
          granularConsent: []
        },
        privacyClassification: values.privacyClassification,
        retentionPolicy: {
          retentionPeriod: values.retentionPeriod,
          retentionReason: values.retentionReason,
          disposalMethod: values.disposalMethod,
          expirationDate: values.retentionExpirationDate.format('YYYY-MM-DD'),
          autoDelete: values.autoDelete
        },
        complianceStatus: values.complianceStatus,
        lastReviewDate: editingDataset?.lastReviewDate,
        nextReviewDate: editingDataset?.nextReviewDate,
        tags: values.tags ? values.tags.split(',').map((tag: string) => tag.trim()) : [],
        owner: values.owner,
        createdAt: editingDataset?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      datasetStorage.save(dataset);
      loadDatasets();
      setModalVisible(false);
      message.success(editingDataset ? 'Dataset updated successfully' : 'Dataset created successfully');
    } catch (error) {
      message.error('Failed to save dataset');
    }
  };

  const getStatusColor = (status: ComplianceStatus) => {
    switch (status) {
      case 'compliant': return 'success';
      case 'non-compliant': return 'error';
      case 'under-review': return 'warning';
      case 'requires-attention': return 'orange';
      default: return 'default';
    }
  };

  const getClassificationColor = (classification: PrivacyClassification) => {
    switch (classification) {
      case 'public': return 'green';
      case 'internal': return 'blue';
      case 'confidential': return 'orange';
      case 'restricted': return 'red';
      case 'highly-sensitive': return 'purple';
      default: return 'default';
    }
  };

  const columns: ColumnsType<Dataset> = [
    {
      title: 'Dataset Name',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (text, record) => (
        <div>
          <Text strong style={{ color: '#1d1d1f' }}>{text}</Text>
          <br />
          <Text style={{ fontSize: '12px', color: '#86868b' }}>
            {record.source}
          </Text>
        </div>
      ),
    },
    {
      title: 'Privacy Classification',
      dataIndex: 'privacyClassification',
      key: 'privacyClassification',
      width: 150,
      render: (classification: PrivacyClassification) => (
        <Tag color={getClassificationColor(classification)} style={{ textTransform: 'capitalize' }}>
          {classification.replace('-', ' ')}
        </Tag>
      ),
    },
    {
      title: 'Compliance Status',
      dataIndex: 'complianceStatus',
      key: 'complianceStatus',
      width: 130,
      render: (status: ComplianceStatus) => (
        <Tag color={getStatusColor(status)} style={{ textTransform: 'capitalize' }}>
          {status.replace('-', ' ')}
        </Tag>
      ),
    },
    {
      title: 'Collection Date',
      dataIndex: 'collectionDate',
      key: 'collectionDate',
      width: 120,
      render: (date: string) => formatDate(date),
    },
    {
      title: 'Retention Expiry',
      dataIndex: ['retentionPolicy', 'expirationDate'],
      key: 'retentionExpiry',
      width: 130,
      render: (date: string) => {
        const daysLeft = calculateDaysUntilExpiry(date);
        const color = daysLeft < 30 ? 'red' : daysLeft < 90 ? 'orange' : 'green';
        return (
          <Tooltip title={`${daysLeft} days remaining`}>
            <Tag color={color}>
              {formatDate(date)}
            </Tag>
          </Tooltip>
        );
      },
    },
    {
      title: 'Owner',
      dataIndex: 'owner',
      key: 'owner',
      width: 150,
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="View Details">
            <Button 
              type="text" 
              icon={<EyeOutlined />} 
              size="small"
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="Edit">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              size="small"
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="Delete">
            <Button 
              type="text" 
              icon={<DeleteOutlined />} 
              size="small"
              danger
              onClick={() => handleDelete(record.id)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2} style={{ margin: 0, color: '#1d1d1f', fontWeight: 600 }}>
          Dataset Management
        </Title>
        <Text style={{ color: '#86868b', fontSize: '16px' }}>
          Manage your customer data catalogue and metadata
        </Text>
      </div>

      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={8} md={6}>
            <Input
              placeholder="Search datasets..."
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              allowClear
            />
          </Col>
          <Col xs={24} sm={8} md={6}>
            <Select
              placeholder="Filter by status"
              value={filterStatus}
              onChange={setFilterStatus}
              style={{ width: '100%' }}
            >
              <Option value="all">All Status</Option>
              <Option value="compliant">Compliant</Option>
              <Option value="non-compliant">Non-Compliant</Option>
              <Option value="under-review">Under Review</Option>
              <Option value="requires-attention">Requires Attention</Option>
            </Select>
          </Col>
          <Col xs={24} sm={8} md={6}>
            <Select
              placeholder="Filter by classification"
              value={filterClassification}
              onChange={setFilterClassification}
              style={{ width: '100%' }}
            >
              <Option value="all">All Classifications</Option>
              <Option value="public">Public</Option>
              <Option value="internal">Internal</Option>
              <Option value="confidential">Confidential</Option>
              <Option value="restricted">Restricted</Option>
              <Option value="highly-sensitive">Highly Sensitive</Option>
            </Select>
          </Col>
          <Col xs={24} sm={24} md={6}>
            <Space>
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={handleAdd}
              >
                Add Dataset
              </Button>
              <Button icon={<ExportOutlined />}>
                Export
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      <Card>
        <Table
          columns={columns}
          dataSource={filteredDatasets}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} datasets`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      <Modal
        title={editingDataset ? 'Edit Dataset' : 'Add New Dataset'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={800}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            collectionFrequency: 'daily',
            consentType: 'explicit',
            privacyClassification: 'internal',
            complianceStatus: 'under-review',
            disposalMethod: 'delete',
            autoDelete: true,
            hasConsent: true
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="Dataset Name"
                rules={[{ required: true, message: 'Please enter dataset name' }]}
              >
                <Input placeholder="Enter dataset name" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="source"
                label="Data Source"
                rules={[{ required: true, message: 'Please enter data source' }]}
              >
                <Input placeholder="Enter data source" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="Description"
            rules={[{ required: true, message: 'Please enter description' }]}
          >
            <TextArea rows={3} placeholder="Enter dataset description" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="collectionDate"
                label="Collection Date"
                rules={[{ required: true, message: 'Please select collection date' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="collectionFrequency"
                label="Collection Frequency"
                rules={[{ required: true, message: 'Please select frequency' }]}
              >
                <Select>
                  <Option value="daily">Daily</Option>
                  <Option value="weekly">Weekly</Option>
                  <Option value="monthly">Monthly</Option>
                  <Option value="quarterly">Quarterly</Option>
                  <Option value="annually">Annually</Option>
                  <Option value="one-time">One-time</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Divider>Privacy & Compliance</Divider>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="privacyClassification"
                label="Privacy Classification"
                rules={[{ required: true, message: 'Please select classification' }]}
              >
                <Select>
                  <Option value="public">Public</Option>
                  <Option value="internal">Internal</Option>
                  <Option value="confidential">Confidential</Option>
                  <Option value="restricted">Restricted</Option>
                  <Option value="highly-sensitive">Highly Sensitive</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="complianceStatus"
                label="Compliance Status"
                rules={[{ required: true, message: 'Please select status' }]}
              >
                <Select>
                  <Option value="compliant">Compliant</Option>
                  <Option value="non-compliant">Non-Compliant</Option>
                  <Option value="under-review">Under Review</Option>
                  <Option value="requires-attention">Requires Attention</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="hasConsent" label="Has Consent" valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="consentType"
                label="Consent Type"
                rules={[{ required: true, message: 'Please select consent type' }]}
              >
                <Select>
                  <Option value="explicit">Explicit</Option>
                  <Option value="implicit">Implicit</Option>
                  <Option value="legitimate-interest">Legitimate Interest</Option>
                  <Option value="none">None</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="consentDate" label="Consent Date">
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="withdrawalMechanism"
            label="Withdrawal Mechanism"
          >
            <Input placeholder="How can consent be withdrawn?" />
          </Form.Item>

          <Divider>Retention Policy</Divider>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="retentionPeriod"
                label="Retention Period (days)"
                rules={[{ required: true, message: 'Please enter retention period' }]}
              >
                <Input type="number" placeholder="365" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="disposalMethod"
                label="Disposal Method"
                rules={[{ required: true, message: 'Please select disposal method' }]}
              >
                <Select>
                  <Option value="delete">Delete</Option>
                  <Option value="anonymize">Anonymize</Option>
                  <Option value="archive">Archive</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="retentionExpirationDate"
                label="Expiration Date"
                rules={[{ required: true, message: 'Please select expiration date' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="retentionReason"
            label="Retention Reason"
            rules={[{ required: true, message: 'Please enter retention reason' }]}
          >
            <TextArea rows={2} placeholder="Why is this data being retained?" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="autoDelete" label="Auto Delete" valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="owner"
                label="Data Owner"
                rules={[{ required: true, message: 'Please enter data owner' }]}
              >
                <Input placeholder="Team or person responsible" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="tags" label="Tags">
            <Input placeholder="Enter tags separated by commas" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                Cancel
              </Button>
              <Button type="primary" htmlType="submit">
                {editingDataset ? 'Update' : 'Create'} Dataset
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default DatasetManagement;
