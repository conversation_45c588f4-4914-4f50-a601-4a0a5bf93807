import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Typography,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  Switch,
  Row,
  Col,
  Divider,
  Steps,
  Alert,
  Progress,
  Tooltip,
  message
} from 'antd';
import {
  PlusOutlined,
  EyeOutlined,
  EditOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  ShieldCheckOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { PrivacyReview, Dataset } from '../types';
import { privacyReviewStorage, datasetStorage, generateId, formatDate } from '../utils/storage';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;
const { Step } = Steps;

const PrivacyReviews: React.FC = () => {
  const [reviews, setReviews] = useState<PrivacyReview[]>([]);
  const [datasets, setDatasets] = useState<Dataset[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingReview, setEditingReview] = useState<PrivacyReview | null>(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [form] = Form.useForm();

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    setLoading(true);
    const loadedReviews = privacyReviewStorage.getAll();
    const loadedDatasets = datasetStorage.getAll();
    setReviews(loadedReviews);
    setDatasets(loadedDatasets);
    setLoading(false);
  };

  const handleAdd = () => {
    setEditingReview(null);
    setCurrentStep(0);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (review: PrivacyReview) => {
    setEditingReview(review);
    setCurrentStep(0);
    form.setFieldsValue({
      ...review,
      reviewDate: dayjs(review.reviewDate),
      nextReviewDate: review.nextReviewDate ? dayjs(review.nextReviewDate) : null,
      approvalDate: review.approvalDate ? dayjs(review.approvalDate) : null,
    });
    setModalVisible(true);
  };

  const handleSubmit = async (values: any) => {
    try {
      const review: PrivacyReview = {
        id: editingReview?.id || generateId(),
        datasetId: values.datasetId,
        reviewType: values.reviewType,
        reviewDate: values.reviewDate.format('YYYY-MM-DD'),
        reviewer: values.reviewer,
        status: values.status,
        privacyImpactAssessment: {
          dataProcessingPurpose: values.dataProcessingPurpose,
          legalBasis: values.legalBasis,
          dataSubjects: values.dataSubjects ? values.dataSubjects.split(',').map((s: string) => s.trim()) : [],
          dataCategories: values.dataCategories ? values.dataCategories.split(',').map((s: string) => s.trim()) : [],
          recipients: values.recipients ? values.recipients.split(',').map((s: string) => s.trim()) : [],
          internationalTransfers: values.internationalTransfers,
          transferMechanisms: values.transferMechanisms ? values.transferMechanisms.split(',').map((s: string) => s.trim()) : [],
          retentionJustification: values.retentionJustification,
          riskLevel: values.riskLevel,
          riskMitigations: values.riskMitigations ? values.riskMitigations.split(',').map((s: string) => s.trim()) : [],
          dpoConsultation: values.dpoConsultation,
          publicConsultation: values.publicConsultation
        },
        findings: editingReview?.findings || [],
        recommendations: values.recommendations ? values.recommendations.split('\n').filter((r: string) => r.trim()) : [],
        nextReviewDate: values.nextReviewDate?.format('YYYY-MM-DD'),
        approvalStatus: values.approvalStatus,
        approver: values.approver,
        approvalDate: values.approvalDate?.format('YYYY-MM-DD')
      };

      privacyReviewStorage.save(review);
      loadData();
      setModalVisible(false);
      message.success(editingReview ? 'Review updated successfully' : 'Review created successfully');
    } catch (error) {
      message.error('Failed to save review');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'in-progress': return 'processing';
      case 'pending': return 'warning';
      case 'requires-action': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircleOutlined />;
      case 'in-progress': return <ClockCircleOutlined />;
      case 'pending': return <ClockCircleOutlined />;
      case 'requires-action': return <ExclamationCircleOutlined />;
      default: return <ClockCircleOutlined />;
    }
  };

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return 'green';
      case 'medium': return 'orange';
      case 'high': return 'red';
      case 'very-high': return 'purple';
      default: return 'default';
    }
  };

  const getDatasetName = (datasetId: string) => {
    const dataset = datasets.find(d => d.id === datasetId);
    return dataset?.name || 'Unknown Dataset';
  };

  const columns: ColumnsType<PrivacyReview> = [
    {
      title: 'Dataset',
      dataIndex: 'datasetId',
      key: 'datasetId',
      width: 200,
      render: (datasetId: string, record) => (
        <div>
          <Text strong style={{ color: '#1d1d1f' }}>{getDatasetName(datasetId)}</Text>
          <br />
          <Text style={{ fontSize: '12px', color: '#86868b' }}>
            {record.reviewType.replace('-', ' ')} review
          </Text>
        </div>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => (
        <Tag color={getStatusColor(status)} icon={getStatusIcon(status)}>
          {status.replace('-', ' ')}
        </Tag>
      ),
    },
    {
      title: 'Risk Level',
      dataIndex: ['privacyImpactAssessment', 'riskLevel'],
      key: 'riskLevel',
      width: 100,
      render: (riskLevel: string) => (
        <Tag color={getRiskColor(riskLevel)} style={{ textTransform: 'capitalize' }}>
          {riskLevel?.replace('-', ' ')}
        </Tag>
      ),
    },
    {
      title: 'Reviewer',
      dataIndex: 'reviewer',
      key: 'reviewer',
      width: 150,
    },
    {
      title: 'Review Date',
      dataIndex: 'reviewDate',
      key: 'reviewDate',
      width: 120,
      render: (date: string) => formatDate(date),
    },
    {
      title: 'Next Review',
      dataIndex: 'nextReviewDate',
      key: 'nextReviewDate',
      width: 120,
      render: (date: string) => date ? formatDate(date) : 'Not scheduled',
    },
    {
      title: 'Approval',
      dataIndex: 'approvalStatus',
      key: 'approvalStatus',
      width: 100,
      render: (status: string) => {
        const color = status === 'approved' ? 'success' : status === 'rejected' ? 'error' : 'warning';
        return <Tag color={color}>{status}</Tag>;
      },
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 100,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="View Details">
            <Button 
              type="text" 
              icon={<EyeOutlined />} 
              size="small"
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="Edit">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              size="small"
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const reviewSteps = [
    {
      title: 'Basic Information',
      description: 'Review details and scope'
    },
    {
      title: 'Privacy Impact Assessment',
      description: 'Assess privacy risks and impacts'
    },
    {
      title: 'Findings & Recommendations',
      description: 'Document findings and next steps'
    },
    {
      title: 'Approval',
      description: 'Review approval and sign-off'
    }
  ];

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="datasetId"
                  label="Dataset"
                  rules={[{ required: true, message: 'Please select a dataset' }]}
                >
                  <Select placeholder="Select dataset to review">
                    {datasets.map(dataset => (
                      <Option key={dataset.id} value={dataset.id}>
                        {dataset.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="reviewType"
                  label="Review Type"
                  rules={[{ required: true, message: 'Please select review type' }]}
                >
                  <Select>
                    <Option value="initial">Initial</Option>
                    <Option value="periodic">Periodic</Option>
                    <Option value="incident-driven">Incident-Driven</Option>
                    <Option value="regulatory">Regulatory</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="reviewDate"
                  label="Review Date"
                  rules={[{ required: true, message: 'Please select review date' }]}
                >
                  <DatePicker style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="reviewer"
                  label="Reviewer"
                  rules={[{ required: true, message: 'Please enter reviewer name' }]}
                >
                  <Input placeholder="Enter reviewer name" />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              name="status"
              label="Review Status"
              rules={[{ required: true, message: 'Please select status' }]}
            >
              <Select>
                <Option value="pending">Pending</Option>
                <Option value="in-progress">In Progress</Option>
                <Option value="completed">Completed</Option>
                <Option value="requires-action">Requires Action</Option>
              </Select>
            </Form.Item>
          </>
        );
      case 1:
        return (
          <>
            <Form.Item
              name="dataProcessingPurpose"
              label="Data Processing Purpose"
              rules={[{ required: true, message: 'Please describe the processing purpose' }]}
            >
              <TextArea rows={3} placeholder="Describe why this data is being processed" />
            </Form.Item>
            <Form.Item
              name="legalBasis"
              label="Legal Basis"
              rules={[{ required: true, message: 'Please specify legal basis' }]}
            >
              <Input placeholder="e.g., Consent, Contract, Legitimate Interest" />
            </Form.Item>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="dataSubjects" label="Data Subjects">
                  <Input placeholder="e.g., Customers, Employees (comma-separated)" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="dataCategories" label="Data Categories">
                  <Input placeholder="e.g., Personal, Financial (comma-separated)" />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item name="recipients" label="Data Recipients">
              <Input placeholder="Who receives this data? (comma-separated)" />
            </Form.Item>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="internationalTransfers" label="International Transfers" valuePropName="checked">
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="transferMechanisms" label="Transfer Mechanisms">
                  <Input placeholder="e.g., SCCs, Adequacy Decision (comma-separated)" />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              name="retentionJustification"
              label="Retention Justification"
              rules={[{ required: true, message: 'Please justify retention period' }]}
            >
              <TextArea rows={2} placeholder="Why is this retention period necessary?" />
            </Form.Item>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="riskLevel"
                  label="Risk Level"
                  rules={[{ required: true, message: 'Please assess risk level' }]}
                >
                  <Select>
                    <Option value="low">Low</Option>
                    <Option value="medium">Medium</Option>
                    <Option value="high">High</Option>
                    <Option value="very-high">Very High</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="riskMitigations" label="Risk Mitigations">
                  <Input placeholder="How are risks mitigated? (comma-separated)" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="dpoConsultation" label="DPO Consultation" valuePropName="checked">
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="publicConsultation" label="Public Consultation" valuePropName="checked">
                  <Switch />
                </Form.Item>
              </Col>
            </Row>
          </>
        );
      case 2:
        return (
          <>
            <Alert
              message="Document your findings and recommendations"
              description="Based on your privacy impact assessment, document any issues found and provide recommendations for improvement."
              type="info"
              showIcon
              style={{ marginBottom: '16px' }}
            />
            <Form.Item
              name="recommendations"
              label="Recommendations"
              rules={[{ required: true, message: 'Please provide recommendations' }]}
            >
              <TextArea 
                rows={6} 
                placeholder="Enter each recommendation on a new line..."
              />
            </Form.Item>
            <Form.Item name="nextReviewDate" label="Next Review Date">
              <DatePicker style={{ width: '100%' }} />
            </Form.Item>
          </>
        );
      case 3:
        return (
          <>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="approvalStatus"
                  label="Approval Status"
                  rules={[{ required: true, message: 'Please select approval status' }]}
                >
                  <Select>
                    <Option value="pending">Pending</Option>
                    <Option value="approved">Approved</Option>
                    <Option value="rejected">Rejected</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="approver" label="Approver">
                  <Input placeholder="Enter approver name" />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item name="approvalDate" label="Approval Date">
              <DatePicker style={{ width: '100%' }} />
            </Form.Item>
          </>
        );
      default:
        return null;
    }
  };

  return (
    <div>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2} style={{ margin: 0, color: '#1d1d1f', fontWeight: 600 }}>
          Privacy Reviews
        </Title>
        <Text style={{ color: '#86868b', fontSize: '16px' }}>
          Manage privacy impact assessments and compliance reviews
        </Text>
      </div>

      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={[16, 16]} align="middle" justify="space-between">
          <Col>
            <Space size="large">
              <div style={{ textAlign: 'center' }}>
                <Text style={{ fontSize: '24px', fontWeight: 600, color: '#1d1d1f' }}>
                  {reviews.length}
                </Text>
                <br />
                <Text style={{ fontSize: '12px', color: '#86868b' }}>Total Reviews</Text>
              </div>
              <div style={{ textAlign: 'center' }}>
                <Text style={{ fontSize: '24px', fontWeight: 600, color: '#34c759' }}>
                  {reviews.filter(r => r.status === 'completed').length}
                </Text>
                <br />
                <Text style={{ fontSize: '12px', color: '#86868b' }}>Completed</Text>
              </div>
              <div style={{ textAlign: 'center' }}>
                <Text style={{ fontSize: '24px', fontWeight: 600, color: '#ff9500' }}>
                  {reviews.filter(r => r.status === 'in-progress').length}
                </Text>
                <br />
                <Text style={{ fontSize: '12px', color: '#86868b' }}>In Progress</Text>
              </div>
              <div style={{ textAlign: 'center' }}>
                <Text style={{ fontSize: '24px', fontWeight: 600, color: '#ff3b30' }}>
                  {reviews.filter(r => r.status === 'requires-action').length}
                </Text>
                <br />
                <Text style={{ fontSize: '12px', color: '#86868b' }}>Requires Action</Text>
              </div>
            </Space>
          </Col>
          <Col>
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              New Privacy Review
            </Button>
          </Col>
        </Row>
      </Card>

      <Card>
        <Table
          columns={columns}
          dataSource={reviews}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} reviews`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      <Modal
        title={
          <Space>
            <ShieldCheckOutlined style={{ color: '#007AFF' }} />
            {editingReview ? 'Edit Privacy Review' : 'New Privacy Review'}
          </Space>
        }
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={900}
        destroyOnClose
      >
        <Steps current={currentStep} style={{ marginBottom: '24px' }}>
          {reviewSteps.map((step, index) => (
            <Step key={index} title={step.title} description={step.description} />
          ))}
        </Steps>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            reviewType: 'periodic',
            status: 'pending',
            riskLevel: 'medium',
            approvalStatus: 'pending',
            internationalTransfers: false,
            dpoConsultation: false,
            publicConsultation: false
          }}
        >
          {renderStepContent()}

          <Form.Item style={{ marginBottom: 0, textAlign: 'right', marginTop: '24px' }}>
            <Space>
              {currentStep > 0 && (
                <Button onClick={() => setCurrentStep(currentStep - 1)}>
                  Previous
                </Button>
              )}
              <Button onClick={() => setModalVisible(false)}>
                Cancel
              </Button>
              {currentStep < reviewSteps.length - 1 ? (
                <Button type="primary" onClick={() => setCurrentStep(currentStep + 1)}>
                  Next
                </Button>
              ) : (
                <Button type="primary" htmlType="submit">
                  {editingReview ? 'Update' : 'Create'} Review
                </Button>
              )}
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default PrivacyReviews;
