/* Authentic Apple Design System */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  /* Official Apple color palette */
  --apple-blue: #007AFF;
  --apple-blue-light: #5AC8FA;
  --apple-blue-dark: #0051D5;
  --apple-gray: #8E8E93;
  --apple-gray-light: #F2F2F7;
  --apple-gray-dark: #1C1C1E;
  --apple-green: #34C759;
  --apple-orange: #FF9500;
  --apple-red: #FF3B30;
  --apple-yellow: #FFCC00;
  --apple-purple: #AF52DE;
  --apple-pink: #FF2D92;
  --apple-indigo: #5856D6;

  /* Semantic colors */
  --background-primary: #FFFFFF;
  --background-secondary: #F8F9FA;
  --background-tertiary: #F2F2F7;
  --text-primary: #1D1D1F;
  --text-secondary: #86868B;
  --text-tertiary: #8E8E93;
  --border-color: #D2D2D7;
  --shadow-color: rgba(0, 0, 0, 0.1);

  /* Typography - Apple SF Pro inspired */
  --font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', Inter, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 32px;

  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;

  /* Border radius */
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;

  /* Shadows */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --background-primary: #000000;
    --background-secondary: #1C1C1E;
    --background-tertiary: #2C2C2E;
    --text-primary: #FFFFFF;
    --text-secondary: #EBEBF5;
    --text-tertiary: #8E8E93;
    --border-color: #38383A;
    --shadow-color: rgba(0, 0, 0, 0.3);
  }
}

/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  color: var(--text-primary);
  background-color: var(--background-primary);
  line-height: 1.6;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

h1 { font-size: var(--font-size-3xl); }
h2 { font-size: var(--font-size-2xl); }
h3 { font-size: var(--font-size-xl); }
h4 { font-size: var(--font-size-lg); }
h5 { font-size: var(--font-size-base); }
h6 { font-size: var(--font-size-sm); }

p {
  margin-bottom: var(--spacing-md);
  color: var(--text-secondary);
}

/* Links */
a {
  color: var(--apple-blue);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--apple-blue-dark);
}

/* Buttons */
.btn-apple {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-base);
  font-weight: 500;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  min-height: 44px; /* Apple's minimum touch target */
}

.btn-apple-primary {
  background-color: var(--apple-blue);
  color: white;
}

.btn-apple-primary:hover {
  background-color: var(--apple-blue-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-apple-secondary {
  background-color: var(--background-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-apple-secondary:hover {
  background-color: var(--background-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

/* Cards */
.card-apple {
  background-color: var(--background-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
}

.card-apple:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

/* Status indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-compliant {
  background-color: rgba(52, 199, 89, 0.1);
  color: var(--apple-green);
}

.status-non-compliant {
  background-color: rgba(255, 59, 48, 0.1);
  color: var(--apple-red);
}

.status-under-review {
  background-color: rgba(255, 149, 0, 0.1);
  color: var(--apple-orange);
}

.status-requires-attention {
  background-color: rgba(255, 204, 0, 0.1);
  color: var(--apple-yellow);
}

/* Privacy classification badges */
.privacy-public {
  background-color: rgba(52, 199, 89, 0.1);
  color: var(--apple-green);
}

.privacy-internal {
  background-color: rgba(0, 122, 255, 0.1);
  color: var(--apple-blue);
}

.privacy-confidential {
  background-color: rgba(255, 149, 0, 0.1);
  color: var(--apple-orange);
}

.privacy-restricted {
  background-color: rgba(255, 59, 48, 0.1);
  color: var(--apple-red);
}

.privacy-highly-sensitive {
  background-color: rgba(175, 82, 222, 0.1);
  color: var(--apple-purple);
}

/* Utility classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }

/* Responsive design */
@media (max-width: 768px) {
  :root {
    --font-size-3xl: 28px;
    --font-size-2xl: 20px;
    --spacing-lg: 16px;
    --spacing-xl: 24px;
  }

  .card-apple {
    padding: var(--spacing-md);
  }
}

/* Ant Design customizations */
.ant-layout {
  background-color: var(--background-primary) !important;
}

.ant-layout-header {
  background-color: var(--background-primary) !important;
  border-bottom: 1px solid var(--border-color) !important;
  padding: 0 var(--spacing-lg) !important;
  height: 64px !important;
  line-height: 64px !important;
}

.ant-menu {
  background-color: transparent !important;
  border: none !important;
}

.ant-menu-item {
  border-radius: var(--radius-md) !important;
  margin: 0 var(--spacing-xs) !important;
}

.ant-card {
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--shadow-sm) !important;
  border-color: var(--border-color) !important;
}

.ant-btn {
  border-radius: var(--radius-md) !important;
  font-weight: 500 !important;
  min-height: 36px !important;
}

.ant-btn-primary {
  background-color: var(--apple-blue) !important;
  border-color: var(--apple-blue) !important;
}

.ant-btn-primary:hover {
  background-color: var(--apple-blue-dark) !important;
  border-color: var(--apple-blue-dark) !important;
}

.ant-table {
  border-radius: var(--radius-lg) !important;
}

.ant-table-thead > tr > th {
  background-color: var(--background-tertiary) !important;
  border-color: var(--border-color) !important;
  font-weight: 600 !important;
}

.ant-form-item-label > label {
  font-weight: 500 !important;
  color: var(--text-primary) !important;
}
