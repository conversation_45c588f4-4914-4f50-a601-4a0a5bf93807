export interface Dataset {
  id: string;
  name: string;
  description: string;
  source: string;
  collectionDate: string;
  collectionFrequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'annually' | 'one-time';
  dataTypes: DataType[];
  schema: SchemaField[];
  consentStatus: ConsentStatus;
  privacyClassification: PrivacyClassification;
  retentionPolicy: RetentionPolicy;
  complianceStatus: ComplianceStatus;
  lastReviewDate?: string;
  nextReviewDate?: string;
  tags: string[];
  owner: string;
  createdAt: string;
  updatedAt: string;
}

export interface DataType {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'date' | 'object' | 'array';
  isPII: boolean;
  isSensitive: boolean;
  description?: string;
}

export interface SchemaField {
  fieldName: string;
  dataType: string;
  isRequired: boolean;
  isPII: boolean;
  description?: string;
  constraints?: string[];
}

export interface ConsentStatus {
  hasConsent: boolean;
  consentType: 'explicit' | 'implicit' | 'legitimate-interest' | 'none';
  consentDate?: string;
  consentExpiry?: string;
  withdrawalMechanism: string;
  granularConsent: GranularConsent[];
}

export interface GranularConsent {
  purpose: string;
  hasConsent: boolean;
  consentDate?: string;
}

export type PrivacyClassification = 'public' | 'internal' | 'confidential' | 'restricted' | 'highly-sensitive';

export interface RetentionPolicy {
  retentionPeriod: number; // in days
  retentionReason: string;
  disposalMethod: 'delete' | 'anonymize' | 'archive';
  expirationDate: string;
  autoDelete: boolean;
}

export type ComplianceStatus = 'compliant' | 'non-compliant' | 'under-review' | 'requires-attention';

export interface PrivacyReview {
  id: string;
  datasetId: string;
  reviewType: 'initial' | 'periodic' | 'incident-driven' | 'regulatory';
  reviewDate: string;
  reviewer: string;
  status: 'pending' | 'in-progress' | 'completed' | 'requires-action';
  privacyImpactAssessment: PrivacyImpactAssessment;
  findings: ReviewFinding[];
  recommendations: string[];
  nextReviewDate?: string;
  approvalStatus: 'pending' | 'approved' | 'rejected';
  approver?: string;
  approvalDate?: string;
}

export interface PrivacyImpactAssessment {
  dataProcessingPurpose: string;
  legalBasis: string;
  dataSubjects: string[];
  dataCategories: string[];
  recipients: string[];
  internationalTransfers: boolean;
  transferMechanisms?: string[];
  retentionJustification: string;
  riskLevel: 'low' | 'medium' | 'high' | 'very-high';
  riskMitigations: string[];
  dpoConsultation: boolean;
  publicConsultation: boolean;
}

export interface ReviewFinding {
  id: string;
  category: 'data-minimization' | 'consent' | 'security' | 'retention' | 'transparency' | 'rights';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  recommendation: string;
  status: 'open' | 'in-progress' | 'resolved' | 'accepted-risk';
  dueDate?: string;
  assignee?: string;
}

export interface ComplianceReport {
  id: string;
  title: string;
  type: 'gdpr' | 'ccpa' | 'hipaa' | 'sox' | 'custom';
  generatedDate: string;
  reportPeriod: {
    startDate: string;
    endDate: string;
  };
  datasets: string[]; // dataset IDs
  metrics: ComplianceMetric[];
  summary: string;
  recommendations: string[];
  status: 'draft' | 'final' | 'submitted';
}

export interface ComplianceMetric {
  name: string;
  value: number;
  unit: string;
  target?: number;
  status: 'good' | 'warning' | 'critical';
  trend: 'improving' | 'stable' | 'declining';
}

export interface FilterOptions {
  privacyClassification?: PrivacyClassification[];
  complianceStatus?: ComplianceStatus[];
  dataTypes?: string[];
  consentStatus?: string[];
  dateRange?: {
    startDate: string;
    endDate: string;
  };
  tags?: string[];
  owner?: string[];
}

export interface DashboardMetrics {
  totalDatasets: number;
  compliantDatasets: number;
  pendingReviews: number;
  expiringSoon: number;
  highRiskDatasets: number;
  consentWithdrawals: number;
  dataBreaches: number;
  complianceScore: number;
}
