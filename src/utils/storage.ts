import { Dataset, PrivacyReview, ComplianceReport } from '../types';

const STORAGE_KEYS = {
  DATASETS: 'apple-privacy-demo-datasets',
  PRIVACY_REVIEWS: 'apple-privacy-demo-privacy-reviews',
  COMPLIANCE_REPORTS: 'apple-privacy-demo-compliance-reports',
} as const;

// Generic storage utilities
export const storage = {
  get: <T>(key: string, defaultValue: T): T => {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      console.error('Error reading from localStorage:', error);
      return defaultValue;
    }
  },

  set: <T>(key: string, value: T): void => {
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error('Error writing to localStorage:', error);
    }
  },

  remove: (key: string): void => {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error('Error removing from localStorage:', error);
    }
  },

  clear: (): void => {
    try {
      localStorage.clear();
    } catch (error) {
      console.error('Error clearing localStorage:', error);
    }
  }
};

// Dataset storage operations
export const datasetStorage = {
  getAll: (): Dataset[] => {
    return storage.get(STORAGE_KEYS.DATASETS, []);
  },

  getById: (id: string): Dataset | undefined => {
    const datasets = datasetStorage.getAll();
    return datasets.find(dataset => dataset.id === id);
  },

  save: (dataset: Dataset): void => {
    const datasets = datasetStorage.getAll();
    const existingIndex = datasets.findIndex(d => d.id === dataset.id);
    
    if (existingIndex >= 0) {
      datasets[existingIndex] = { ...dataset, updatedAt: new Date().toISOString() };
    } else {
      datasets.push({ ...dataset, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() });
    }
    
    storage.set(STORAGE_KEYS.DATASETS, datasets);
  },

  delete: (id: string): void => {
    const datasets = datasetStorage.getAll();
    const filteredDatasets = datasets.filter(dataset => dataset.id !== id);
    storage.set(STORAGE_KEYS.DATASETS, filteredDatasets);
  },

  bulkSave: (datasets: Dataset[]): void => {
    storage.set(STORAGE_KEYS.DATASETS, datasets);
  }
};

// Privacy review storage operations
export const privacyReviewStorage = {
  getAll: (): PrivacyReview[] => {
    return storage.get(STORAGE_KEYS.PRIVACY_REVIEWS, []);
  },

  getById: (id: string): PrivacyReview | undefined => {
    const reviews = privacyReviewStorage.getAll();
    return reviews.find(review => review.id === id);
  },

  getByDatasetId: (datasetId: string): PrivacyReview[] => {
    const reviews = privacyReviewStorage.getAll();
    return reviews.filter(review => review.datasetId === datasetId);
  },

  save: (review: PrivacyReview): void => {
    const reviews = privacyReviewStorage.getAll();
    const existingIndex = reviews.findIndex(r => r.id === review.id);
    
    if (existingIndex >= 0) {
      reviews[existingIndex] = review;
    } else {
      reviews.push(review);
    }
    
    storage.set(STORAGE_KEYS.PRIVACY_REVIEWS, reviews);
  },

  delete: (id: string): void => {
    const reviews = privacyReviewStorage.getAll();
    const filteredReviews = reviews.filter(review => review.id !== id);
    storage.set(STORAGE_KEYS.PRIVACY_REVIEWS, filteredReviews);
  },

  bulkSave: (reviews: PrivacyReview[]): void => {
    storage.set(STORAGE_KEYS.PRIVACY_REVIEWS, reviews);
  }
};

// Compliance report storage operations
export const complianceReportStorage = {
  getAll: (): ComplianceReport[] => {
    return storage.get(STORAGE_KEYS.COMPLIANCE_REPORTS, []);
  },

  getById: (id: string): ComplianceReport | undefined => {
    const reports = complianceReportStorage.getAll();
    return reports.find(report => report.id === id);
  },

  save: (report: ComplianceReport): void => {
    const reports = complianceReportStorage.getAll();
    const existingIndex = reports.findIndex(r => r.id === report.id);
    
    if (existingIndex >= 0) {
      reports[existingIndex] = report;
    } else {
      reports.push(report);
    }
    
    storage.set(STORAGE_KEYS.COMPLIANCE_REPORTS, reports);
  },

  delete: (id: string): void => {
    const reports = complianceReportStorage.getAll();
    const filteredReports = reports.filter(report => report.id !== id);
    storage.set(STORAGE_KEYS.COMPLIANCE_REPORTS, filteredReports);
  },

  bulkSave: (reports: ComplianceReport[]): void => {
    storage.set(STORAGE_KEYS.COMPLIANCE_REPORTS, reports);
  }
};

// Initialize storage with mock data if empty
export const initializeStorage = (mockDatasets: Dataset[], mockReviews: PrivacyReview[], mockReports: ComplianceReport[]): void => {
  if (datasetStorage.getAll().length === 0) {
    datasetStorage.bulkSave(mockDatasets);
  }
  
  if (privacyReviewStorage.getAll().length === 0) {
    privacyReviewStorage.bulkSave(mockReviews);
  }
  
  if (complianceReportStorage.getAll().length === 0) {
    complianceReportStorage.bulkSave(mockReports);
  }
};

// Utility functions for data manipulation
export const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

export const formatDate = (date: string | Date): string => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

export const calculateDaysUntilExpiry = (expiryDate: string): number => {
  const today = new Date();
  const expiry = new Date(expiryDate);
  const diffTime = expiry.getTime() - today.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};
